{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/utils/dateUtils.ts"], "sourcesContent": ["// Helper function to format time\r\nexport const formatMessageTime = (\r\n  dateInput: Date | string | number | undefined | null,\r\n): string => {\r\n  // If no date provided, return empty string\r\n  if (!dateInput) return \"\";\r\n\r\n  // Ensure we have a proper Date object\r\n  let date: Date;\r\n  try {\r\n    if (dateInput instanceof Date) {\r\n      date = dateInput;\r\n    } else if (typeof dateInput === \"number\") {\r\n      date = new Date(dateInput);\r\n    } else if (typeof dateInput === \"string\") {\r\n      // Handle ISO string or timestamp string\r\n      date = new Date(dateInput);\r\n    } else {\r\n      console.error(\"Invalid date input type:\", typeof dateInput);\r\n      return \"\";\r\n    }\r\n\r\n    // Check if date is valid\r\n    if (isNaN(date.getTime())) {\r\n      console.error(\"Invalid date:\", dateInput);\r\n      return \"\";\r\n    }\r\n\r\n    const now = new Date();\r\n    const diff = now.getTime() - date.getTime();\r\n\r\n    // Less than a day\r\n    if (diff < 86400000) {\r\n      return date.toLocaleTimeString(\"vi-VN\", {\r\n        hour: \"2-digit\",\r\n        minute: \"2-digit\",\r\n      });\r\n    }\r\n\r\n    // Less than a week\r\n    if (diff < 604800000) {\r\n      const days = [\"CN\", \"T2\", \"T3\", \"T4\", \"T5\", \"T6\", \"T7\"];\r\n      return days[date.getDay()];\r\n    }\r\n\r\n    // More than a week\r\n    return date.toLocaleDateString(\"vi-VN\", {\r\n      day: \"2-digit\",\r\n      month: \"2-digit\",\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error formatting message time:\", error);\r\n    return \"\";\r\n  }\r\n};\r\n\r\n// Format date for message groups\r\nexport const formatMessageDate = (\r\n  dateInput: Date | string | number | undefined | null,\r\n): string => {\r\n  // If no date provided, return empty string\r\n  if (!dateInput) return \"\";\r\n\r\n  // Ensure we have a proper Date object\r\n  let date: Date;\r\n  try {\r\n    if (dateInput instanceof Date) {\r\n      date = dateInput;\r\n    } else if (typeof dateInput === \"number\") {\r\n      date = new Date(dateInput);\r\n    } else if (typeof dateInput === \"string\") {\r\n      // Handle ISO string or timestamp string\r\n      date = new Date(dateInput);\r\n    } else {\r\n      console.error(\"Invalid date input type:\", typeof dateInput);\r\n      return \"\";\r\n    }\r\n\r\n    // Check if date is valid\r\n    if (isNaN(date.getTime())) {\r\n      console.error(\"Invalid date:\", dateInput);\r\n      return \"\";\r\n    }\r\n\r\n    const now = new Date();\r\n    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\r\n    const yesterday = new Date(today);\r\n    yesterday.setDate(yesterday.getDate() - 1);\r\n\r\n    const messageDate = new Date(\r\n      date.getFullYear(),\r\n      date.getMonth(),\r\n      date.getDate(),\r\n    );\r\n\r\n    if (messageDate.getTime() === today.getTime()) {\r\n      return \"Hôm nay\";\r\n    } else if (messageDate.getTime() === yesterday.getTime()) {\r\n      return \"Hôm qua\";\r\n    } else {\r\n      return date.toLocaleDateString(\"vi-VN\", {\r\n        day: \"2-digit\",\r\n        month: \"2-digit\",\r\n        year: \"numeric\",\r\n      });\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error formatting message date:\", error);\r\n    return \"\";\r\n  }\r\n};\r\n\r\n// Format last activity time in a user-friendly way\r\nexport const formatLastActivity = (\r\n  dateInput: Date | string | number | undefined | null,\r\n): string => {\r\n  // If no date provided, return appropriate message\r\n  if (!dateInput) return \"Không có thông tin\";\r\n\r\n  // Ensure we have a proper Date object\r\n  let date: Date;\r\n  try {\r\n    if (dateInput instanceof Date) {\r\n      date = dateInput;\r\n    } else if (typeof dateInput === \"number\") {\r\n      date = new Date(dateInput);\r\n    } else if (typeof dateInput === \"string\") {\r\n      // Handle ISO string or timestamp string\r\n      date = new Date(dateInput);\r\n    } else {\r\n      console.error(\"Invalid date input type:\", typeof dateInput);\r\n      return \"Không có thông tin\";\r\n    }\r\n\r\n    // Check if date is valid\r\n    if (isNaN(date.getTime())) {\r\n      console.error(\"Invalid date:\", dateInput);\r\n      return \"Không có thông tin\";\r\n    }\r\n\r\n    const now = new Date();\r\n    const diffInMs = now.getTime() - date.getTime();\r\n    const diffInSeconds = Math.floor(diffInMs / 1000);\r\n    const diffInMinutes = Math.floor(diffInSeconds / 60);\r\n    const diffInHours = Math.floor(diffInMinutes / 60);\r\n    const diffInDays = Math.floor(diffInHours / 24);\r\n\r\n    // Format based on how long ago the activity was\r\n    if (diffInSeconds < 60) {\r\n      return \"Vừa mới truy cập\";\r\n    } else if (diffInMinutes < 60) {\r\n      return `${diffInMinutes} phút trước`;\r\n    } else if (diffInHours < 24) {\r\n      return `${diffInHours} giờ trước`;\r\n    } else if (diffInDays < 7) {\r\n      return `${diffInDays} ngày trước`;\r\n    } else {\r\n      // For older dates, show the full date\r\n      return date.toLocaleDateString(\"vi-VN\", {\r\n        day: \"2-digit\",\r\n        month: \"2-digit\",\r\n        year: \"numeric\",\r\n        hour: \"2-digit\",\r\n        minute: \"2-digit\",\r\n      });\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error formatting last activity:\", error);\r\n    return \"Không có thông tin\";\r\n  }\r\n};\r\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;AAC1B,MAAM,oBAAoB,CAC/B;IAEA,2CAA2C;IAC3C,IAAI,CAAC,WAAW,OAAO;IAEvB,sCAAsC;IACtC,IAAI;IACJ,IAAI;QACF,IAAI,qBAAqB,MAAM;YAC7B,OAAO;QACT,OAAO,IAAI,OAAO,cAAc,UAAU;YACxC,OAAO,IAAI,KAAK;QAClB,OAAO,IAAI,OAAO,cAAc,UAAU;YACxC,wCAAwC;YACxC,OAAO,IAAI,KAAK;QAClB,OAAO;YACL,QAAQ,KAAK,CAAC,4BAA4B,OAAO;YACjD,OAAO;QACT;QAEA,yBAAyB;QACzB,IAAI,MAAM,KAAK,OAAO,KAAK;YACzB,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,OAAO;QACT;QAEA,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,OAAO,KAAK,KAAK,OAAO;QAEzC,kBAAkB;QAClB,IAAI,OAAO,UAAU;YACnB,OAAO,KAAK,kBAAkB,CAAC,SAAS;gBACtC,MAAM;gBACN,QAAQ;YACV;QACF;QAEA,mBAAmB;QACnB,IAAI,OAAO,WAAW;YACpB,MAAM,OAAO;gBAAC;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;aAAK;YACvD,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG;QAC5B;QAEA,mBAAmB;QACnB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,KAAK;YACL,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;IACT;AACF;AAGO,MAAM,oBAAoB,CAC/B;IAEA,2CAA2C;IAC3C,IAAI,CAAC,WAAW,OAAO;IAEvB,sCAAsC;IACtC,IAAI;IACJ,IAAI;QACF,IAAI,qBAAqB,MAAM;YAC7B,OAAO;QACT,OAAO,IAAI,OAAO,cAAc,UAAU;YACxC,OAAO,IAAI,KAAK;QAClB,OAAO,IAAI,OAAO,cAAc,UAAU;YACxC,wCAAwC;YACxC,OAAO,IAAI,KAAK;QAClB,OAAO;YACL,QAAQ,KAAK,CAAC,4BAA4B,OAAO;YACjD,OAAO;QACT;QAEA,yBAAyB;QACzB,IAAI,MAAM,KAAK,OAAO,KAAK;YACzB,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,OAAO;QACT;QAEA,MAAM,MAAM,IAAI;QAChB,MAAM,QAAQ,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,IAAI,IAAI,OAAO;QACrE,MAAM,YAAY,IAAI,KAAK;QAC3B,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;QAExC,MAAM,cAAc,IAAI,KACtB,KAAK,WAAW,IAChB,KAAK,QAAQ,IACb,KAAK,OAAO;QAGd,IAAI,YAAY,OAAO,OAAO,MAAM,OAAO,IAAI;YAC7C,OAAO;QACT,OAAO,IAAI,YAAY,OAAO,OAAO,UAAU,OAAO,IAAI;YACxD,OAAO;QACT,OAAO;YACL,OAAO,KAAK,kBAAkB,CAAC,SAAS;gBACtC,KAAK;gBACL,OAAO;gBACP,MAAM;YACR;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;IACT;AACF;AAGO,MAAM,qBAAqB,CAChC;IAEA,kDAAkD;IAClD,IAAI,CAAC,WAAW,OAAO;IAEvB,sCAAsC;IACtC,IAAI;IACJ,IAAI;QACF,IAAI,qBAAqB,MAAM;YAC7B,OAAO;QACT,OAAO,IAAI,OAAO,cAAc,UAAU;YACxC,OAAO,IAAI,KAAK;QAClB,OAAO,IAAI,OAAO,cAAc,UAAU;YACxC,wCAAwC;YACxC,OAAO,IAAI,KAAK;QAClB,OAAO;YACL,QAAQ,KAAK,CAAC,4BAA4B,OAAO;YACjD,OAAO;QACT;QAEA,yBAAyB;QACzB,IAAI,MAAM,KAAK,OAAO,KAAK;YACzB,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,OAAO;QACT;QAEA,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,IAAI,OAAO,KAAK,KAAK,OAAO;QAC7C,MAAM,gBAAgB,KAAK,KAAK,CAAC,WAAW;QAC5C,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;QACjD,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;QAC/C,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;QAE5C,gDAAgD;QAChD,IAAI,gBAAgB,IAAI;YACtB,OAAO;QACT,OAAO,IAAI,gBAAgB,IAAI;YAC7B,OAAO,GAAG,cAAc,WAAW,CAAC;QACtC,OAAO,IAAI,cAAc,IAAI;YAC3B,OAAO,GAAG,YAAY,UAAU,CAAC;QACnC,OAAO,IAAI,aAAa,GAAG;YACzB,OAAO,GAAG,WAAW,WAAW,CAAC;QACnC,OAAO;YACL,sCAAsC;YACtC,OAAO,KAAK,kBAAkB,CAAC,SAAS;gBACtC,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,QAAQ;YACV;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/utils/reactionUtils.ts"], "sourcesContent": ["import { ReactionType } from \"@/types/base\";\r\nimport { Emoji, EmojiStyle } from \"emoji-picker-react\";\r\nimport React from \"react\";\r\n\r\n// Type for the emoji style parameter\r\ntype EmojiStyleType = EmojiStyle | string;\r\n\r\n// Type for the Emoji component props\r\ninterface EmojiProps {\r\n  unified: string;\r\n  size: number;\r\n  emojiStyle: EmojiStyle;\r\n}\r\n\r\n// Define a type for reaction objects with varying structures\r\nexport interface ReactionObject {\r\n  userId: string;\r\n  reactionType?: ReactionType;\r\n  reaction?: string;\r\n  count?: number;\r\n}\r\n\r\n// Helper function to get reaction emoji unified codes\r\nexport const getReactionUnifiedCodes = (): Record<ReactionType, string> => ({\r\n  [ReactionType.LIKE]: \"1f44d\",\r\n  [ReactionType.LOVE]: \"2764-fe0f\",\r\n  [ReactionType.HAHA]: \"1f602\",\r\n  [ReactionType.WOW]: \"1f62e\",\r\n  [ReactionType.SAD]: \"1f622\",\r\n  [ReactionType.ANGRY]: \"1f621\",\r\n});\r\n\r\n// Helper function to get reaction labels\r\nexport const getReactionLabels = (): Record<ReactionType, string> => ({\r\n  [ReactionType.LIKE]: \"Thích\",\r\n  [ReactionType.LOVE]: \"Yêu thích\",\r\n  [ReactionType.HAHA]: \"Haha\",\r\n  [ReactionType.WOW]: \"Wow\",\r\n  [ReactionType.SAD]: \"Buồn\",\r\n  [ReactionType.ANGRY]: \"Phẫn nộ\",\r\n});\r\n\r\n// Helper function to get reaction type from object\r\nexport const getReactionTypeFromObject = (\r\n  reaction: ReactionObject,\r\n): ReactionType => {\r\n  // Check if the reaction object has a reactionType property\r\n  if (\"reactionType\" in reaction && reaction.reactionType) {\r\n    return reaction.reactionType;\r\n  }\r\n  // Check if the reaction object has a reaction property\r\n  else if (\"reaction\" in reaction && typeof reaction.reaction === \"string\") {\r\n    return reaction.reaction as ReactionType;\r\n  }\r\n  // Default to LIKE if neither property is found\r\n  return ReactionType.LIKE;\r\n};\r\n\r\n// Helper function to get reaction count\r\nexport const getReactionCount = (reaction: ReactionObject): number => {\r\n  // Check if the reaction has a count property\r\n  if (\"count\" in reaction && typeof reaction.count === \"number\") {\r\n    return reaction.count;\r\n  }\r\n  return 1; // Default count\r\n};\r\n\r\n// Process reactions to count by type\r\nexport const processReactions = (reactions: ReactionObject[]) => {\r\n  const reactionCounts: Record<ReactionType, number> = {} as Record<\r\n    ReactionType,\r\n    number\r\n  >;\r\n  let totalReactions = 0;\r\n\r\n  // Process each reaction to handle different API response formats\r\n  reactions.forEach((reaction) => {\r\n    const reactionType = getReactionTypeFromObject(reaction);\r\n    const count = getReactionCount(reaction);\r\n\r\n    reactionCounts[reactionType] = (reactionCounts[reactionType] || 0) + count;\r\n    totalReactions += count;\r\n  });\r\n\r\n  return { reactionCounts, totalReactions };\r\n};\r\n\r\n// Reusable component for rendering an emoji with proper centering\r\nexport const renderCenteredEmoji = (\r\n  unified: string,\r\n  size: number,\r\n  emojiStyle: EmojiStyleType,\r\n) => {\r\n  return React.createElement(\r\n    \"div\",\r\n    { className: \"flex items-center justify-center w-full h-full\" },\r\n    React.createElement(\r\n      \"div\",\r\n      {\r\n        className: \"flex items-center justify-center\",\r\n        style: { lineHeight: 0 },\r\n      },\r\n      React.createElement<EmojiProps>(Emoji, {\r\n        unified,\r\n        size,\r\n        emojiStyle: emojiStyle as EmojiStyle,\r\n      }),\r\n    ),\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;;;;AAqBO,MAAM,0BAA0B,IAAoC,CAAC;QAC1E,CAAC,oHAAA,CAAA,eAAY,CAAC,IAAI,CAAC,EAAE;QACrB,CAAC,oHAAA,CAAA,eAAY,CAAC,IAAI,CAAC,EAAE;QACrB,CAAC,oHAAA,CAAA,eAAY,CAAC,IAAI,CAAC,EAAE;QACrB,CAAC,oHAAA,CAAA,eAAY,CAAC,GAAG,CAAC,EAAE;QACpB,CAAC,oHAAA,CAAA,eAAY,CAAC,GAAG,CAAC,EAAE;QACpB,CAAC,oHAAA,CAAA,eAAY,CAAC,KAAK,CAAC,EAAE;IACxB,CAAC;AAGM,MAAM,oBAAoB,IAAoC,CAAC;QACpE,CAAC,oHAAA,CAAA,eAAY,CAAC,IAAI,CAAC,EAAE;QACrB,CAAC,oHAAA,CAAA,eAAY,CAAC,IAAI,CAAC,EAAE;QACrB,CAAC,oHAAA,CAAA,eAAY,CAAC,IAAI,CAAC,EAAE;QACrB,CAAC,oHAAA,CAAA,eAAY,CAAC,GAAG,CAAC,EAAE;QACpB,CAAC,oHAAA,CAAA,eAAY,CAAC,GAAG,CAAC,EAAE;QACpB,CAAC,oHAAA,CAAA,eAAY,CAAC,KAAK,CAAC,EAAE;IACxB,CAAC;AAGM,MAAM,4BAA4B,CACvC;IAEA,2DAA2D;IAC3D,IAAI,kBAAkB,YAAY,SAAS,YAAY,EAAE;QACvD,OAAO,SAAS,YAAY;IAC9B,OAEK,IAAI,cAAc,YAAY,OAAO,SAAS,QAAQ,KAAK,UAAU;QACxE,OAAO,SAAS,QAAQ;IAC1B;IACA,+CAA+C;IAC/C,OAAO,oHAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAGO,MAAM,mBAAmB,CAAC;IAC/B,6CAA6C;IAC7C,IAAI,WAAW,YAAY,OAAO,SAAS,KAAK,KAAK,UAAU;QAC7D,OAAO,SAAS,KAAK;IACvB;IACA,OAAO,GAAG,gBAAgB;AAC5B;AAGO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,iBAA+C,CAAC;IAItD,IAAI,iBAAiB;IAErB,iEAAiE;IACjE,UAAU,OAAO,CAAC,CAAC;QACjB,MAAM,eAAe,0BAA0B;QAC/C,MAAM,QAAQ,iBAAiB;QAE/B,cAAc,CAAC,aAAa,GAAG,CAAC,cAAc,CAAC,aAAa,IAAI,CAAC,IAAI;QACrE,kBAAkB;IACpB;IAEA,OAAO;QAAE;QAAgB;IAAe;AAC1C;AAGO,MAAM,sBAAsB,CACjC,SACA,MACA;IAEA,qBAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CACxB,OACA;QAAE,WAAW;IAAiD,iBAC9D,qMAAA,CAAA,UAAK,CAAC,aAAa,CACjB,OACA;QACE,WAAW;QACX,OAAO;YAAE,YAAY;QAAE;IACzB,iBACA,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAa,mLAAA,CAAA,QAAK,EAAE;QACrC;QACA;QACA,YAAY;IACd;AAGN", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/utils/link-utils.tsx"], "sourcesContent": ["import { ReactNode } from \"react\";\r\nimport { ExternalLink } from \"lucide-react\";\r\n\r\n/**\r\n * Lấy icon cho c<PERSON><PERSON> trang web phổ biến\r\n * @param domain Tê<PERSON> miền của trang web\r\n * @returns ReactNode chứa icon tương ứng\r\n */\r\nexport function getLinkIcon(domain: string): ReactNode {\r\n  // Kiểm tra domain và trả về icon tương ứng\r\n  if (domain.includes(\"instagram.com\")) {\r\n    return (\r\n      <div className=\"bg-gradient-to-tr from-yellow-500 via-pink-500 to-purple-500 w-full h-full flex items-center justify-center\">\r\n        <svg viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"white\">\r\n          <path d=\"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\" />\r\n        </svg>\r\n      </div>\r\n    );\r\n  } else if (domain.includes(\"tiktok.com\")) {\r\n    return (\r\n      <div className=\"bg-black w-full h-full flex items-center justify-center\">\r\n        <svg viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"white\">\r\n          <path d=\"M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z\" />\r\n        </svg>\r\n      </div>\r\n    );\r\n  } else if (domain.includes(\"facebook.com\")) {\r\n    return (\r\n      <div className=\"bg-blue-600 w-full h-full flex items-center justify-center\">\r\n        <svg viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"white\">\r\n          <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\" />\r\n        </svg>\r\n      </div>\r\n    );\r\n  } else if (domain.includes(\"youtube.com\") || domain.includes(\"youtu.be\")) {\r\n    return (\r\n      <div className=\"bg-red-600 w-full h-full flex items-center justify-center\">\r\n        <svg viewBox=\"0 0 24 24\" width=\"24\" height=\"24\" fill=\"white\">\r\n          <path d=\"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z\" />\r\n        </svg>\r\n      </div>\r\n    );\r\n  } else if (domain.includes(\"lazada.vn\")) {\r\n    return (\r\n      <div className=\"bg-blue-500 w-full h-full flex items-center justify-center\">\r\n        <span className=\"text-white font-bold text-lg\">L</span>\r\n      </div>\r\n    );\r\n  } else if (domain.includes(\"shopee.vn\")) {\r\n    return (\r\n      <div className=\"bg-orange-500 w-full h-full flex items-center justify-center\">\r\n        <span className=\"text-white font-bold text-lg\">S</span>\r\n      </div>\r\n    );\r\n  } else if (domain.includes(\"tiki.vn\")) {\r\n    return (\r\n      <div className=\"bg-blue-400 w-full h-full flex items-center justify-center\">\r\n        <span className=\"text-white font-bold text-lg\">T</span>\r\n      </div>\r\n    );\r\n  } else if (domain.includes(\"bataptracnghiem.com\")) {\r\n    return (\r\n      <div className=\"bg-yellow-400 w-full h-full flex items-center justify-center\">\r\n        <span className=\"text-white font-bold text-lg\">B</span>\r\n      </div>\r\n    );\r\n  } else if (domain.includes(\"azota.vn\")) {\r\n    return (\r\n      <div className=\"bg-blue-700 w-full h-full flex items-center justify-center\">\r\n        <span className=\"text-white font-bold text-lg\">A</span>\r\n      </div>\r\n    );\r\n  } else {\r\n    // Default icon for other domains\r\n    return <ExternalLink className=\"h-5 w-5 text-gray-500\" />;\r\n  }\r\n}\r\n\r\n/**\r\n * Lấy tiêu đề cho các trang web phổ biến\r\n * @param domain Tên miền của trang web\r\n * @param defaultTitle Tiêu đề mặc định nếu không tìm thấy\r\n * @returns Tiêu đề tương ứng\r\n */\r\nexport function getLinkTitle(domain: string, defaultTitle: string): string {\r\n  // Kiểm tra domain và trả về tiêu đề tương ứng\r\n  if (domain.includes(\"instagram.com\")) {\r\n    return \"Instagram\";\r\n  } else if (domain.includes(\"tiktok.com\")) {\r\n    return \"TikTok Video\";\r\n  } else if (domain.includes(\"facebook.com\")) {\r\n    return \"Facebook Link\";\r\n  } else if (domain.includes(\"youtube.com\") || domain.includes(\"youtu.be\")) {\r\n    return \"YouTube Video\";\r\n  } else if (domain.includes(\"lazada.vn\")) {\r\n    return \"Lazada Product\";\r\n  } else if (domain.includes(\"shopee.vn\")) {\r\n    return \"Shopee Product\";\r\n  } else if (domain.includes(\"tiki.vn\")) {\r\n    return \"Tiki Product\";\r\n  } else if (domain.includes(\"bataptracnghiem.com\")) {\r\n    return \"Thi thử trắc nghiệm\";\r\n  } else if (domain.includes(\"azota.vn\")) {\r\n    return \"Ôn tập TTHCM\";\r\n  } else {\r\n    // Trả về tiêu đề mặc định cho các domain khác\r\n    return defaultTitle;\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;;;AAOO,SAAS,YAAY,MAAc;IACxC,2CAA2C;IAC3C,IAAI,OAAO,QAAQ,CAAC,kBAAkB;QACpC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,SAAQ;gBAAY,OAAM;gBAAK,QAAO;gBAAK,MAAK;0BACnD,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;;;;;;IAIhB,OAAO,IAAI,OAAO,QAAQ,CAAC,eAAe;QACxC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,SAAQ;gBAAY,OAAM;gBAAK,QAAO;gBAAK,MAAK;0BACnD,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;;;;;;IAIhB,OAAO,IAAI,OAAO,QAAQ,CAAC,iBAAiB;QAC1C,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,SAAQ;gBAAY,OAAM;gBAAK,QAAO;gBAAK,MAAK;0BACnD,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;;;;;;IAIhB,OAAO,IAAI,OAAO,QAAQ,CAAC,kBAAkB,OAAO,QAAQ,CAAC,aAAa;QACxE,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,SAAQ;gBAAY,OAAM;gBAAK,QAAO;gBAAK,MAAK;0BACnD,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;;;;;;IAIhB,OAAO,IAAI,OAAO,QAAQ,CAAC,cAAc;QACvC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAK,WAAU;0BAA+B;;;;;;;;;;;IAGrD,OAAO,IAAI,OAAO,QAAQ,CAAC,cAAc;QACvC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAK,WAAU;0BAA+B;;;;;;;;;;;IAGrD,OAAO,IAAI,OAAO,QAAQ,CAAC,YAAY;QACrC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAK,WAAU;0BAA+B;;;;;;;;;;;IAGrD,OAAO,IAAI,OAAO,QAAQ,CAAC,wBAAwB;QACjD,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAK,WAAU;0BAA+B;;;;;;;;;;;IAGrD,OAAO,IAAI,OAAO,QAAQ,CAAC,aAAa;QACtC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAK,WAAU;0BAA+B;;;;;;;;;;;IAGrD,OAAO;QACL,iCAAiC;QACjC,qBAAO,8OAAC,sNAAA,CAAA,eAAY;YAAC,WAAU;;;;;;IACjC;AACF;AAQO,SAAS,aAAa,MAAc,EAAE,YAAoB;IAC/D,8CAA8C;IAC9C,IAAI,OAAO,QAAQ,CAAC,kBAAkB;QACpC,OAAO;IACT,OAAO,IAAI,OAAO,QAAQ,CAAC,eAAe;QACxC,OAAO;IACT,OAAO,IAAI,OAAO,QAAQ,CAAC,iBAAiB;QAC1C,OAAO;IACT,OAAO,IAAI,OAAO,QAAQ,CAAC,kBAAkB,OAAO,QAAQ,CAAC,aAAa;QACxE,OAAO;IACT,OAAO,IAAI,OAAO,QAAQ,CAAC,cAAc;QACvC,OAAO;IACT,OAAO,IAAI,OAAO,QAAQ,CAAC,cAAc;QACvC,OAAO;IACT,OAAO,IAAI,OAAO,QAAQ,CAAC,YAAY;QACrC,OAAO;IACT,OAAO,IAAI,OAAO,QAAQ,CAAC,wBAAwB;QACjD,OAAO;IACT,OAAO,IAAI,OAAO,QAAQ,CAAC,aAAa;QACtC,OAAO;IACT,OAAO;QACL,8CAA8C;QAC9C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 482, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/hooks/useDebounce.ts"], "sourcesContent": ["import { useState, useEffect } from \"react\";\r\n\r\n/**\r\n * A hook that debounces a value\r\n * @param value The value to debounce\r\n * @param delay The delay in milliseconds\r\n * @returns The debounced value\r\n */\r\nexport function useDebounce<T>(value: T, delay: number): T {\r\n  const [debouncedValue, setDebouncedValue] = useState<T>(value);\r\n\r\n  useEffect(() => {\r\n    // Update debounced value after delay\r\n    const handler = setTimeout(() => {\r\n      setDebouncedValue(value);\r\n    }, delay);\r\n\r\n    // Cancel the timeout if value changes or component unmounts\r\n    return () => {\r\n      clearTimeout(handler);\r\n    };\r\n  }, [value, delay]);\r\n\r\n  return debouncedValue;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAQO,SAAS,YAAe,KAAQ,EAAE,KAAa;IACpD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAK;IAExD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qCAAqC;QACrC,MAAM,UAAU,WAAW;YACzB,kBAAkB;QACpB,GAAG;QAEH,4DAA4D;QAC5D,OAAO;YACL,aAAa;QACf;IACF,GAAG;QAAC;QAAO;KAAM;IAEjB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 510, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/ai.action.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createAxiosInstance } from \"@/lib/axios\";\r\n\r\n// Define interfaces for AI enhance feature\r\ninterface PreviousMessage {\r\n  content: string;\r\n  type: string;\r\n  senderId: string;\r\n  senderName: string;\r\n}\r\n\r\ninterface EnhanceMessageResponse {\r\n  enhancedMessage: string;\r\n}\r\n\r\n/**\r\n * Enhance a message using AI\r\n * @param message The message to enhance\r\n * @param previousMessages Optional array of previous messages for context\r\n * @returns Enhanced message\r\n */\r\nexport async function enhanceMessage(\r\n  message: string,\r\n  previousMessages?: PreviousMessage[],\r\n) {\r\n  try {\r\n    const axiosInstance = createAxiosInstance();\r\n    console.log(\"axiosInstance\", axiosInstance.defaults.baseURL);\r\n    const response = await axiosInstance.post(\"/ai/enhance\", {\r\n      message,\r\n      previousMessages,\r\n    });\r\n\r\n    const result = response.data as EnhanceMessageResponse;\r\n    return { success: true, enhancedMessage: result.enhancedMessage };\r\n  } catch (error) {\r\n    console.error(\"AI message enhancement failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Generate AI response based on a prompt\r\n * @param prompt The prompt for AI generation\r\n * @returns Generated AI response\r\n */\r\nexport async function generateAIResponse(prompt: string) {\r\n  try {\r\n    const axiosInstance = createAxiosInstance();\r\n\r\n    const response = await axiosInstance.post(\"/ai/generate\", {\r\n      prompt,\r\n    });\r\n\r\n    return { success: true, response: response.data.response };\r\n  } catch (error) {\r\n    console.error(\"AI response generation failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Summarize text using AI\r\n * @param text The text to summarize\r\n * @param maxLength Optional maximum length for the summary\r\n * @param previousMessages Optional array of previous messages for context\r\n * @returns Summarized text\r\n */\r\nexport async function summarizeText(\r\n  text: string,\r\n  maxLength?: number,\r\n  previousMessages?: PreviousMessage[],\r\n) {\r\n  try {\r\n    const axiosInstance = createAxiosInstance();\r\n\r\n    const payload: any = { text };\r\n\r\n    if (maxLength) {\r\n      payload.maxLength = maxLength.toString();\r\n    }\r\n\r\n    if (previousMessages) {\r\n      payload.previousMessages = previousMessages;\r\n    }\r\n\r\n    const response = await axiosInstance.post(\"/ai/summarize\", payload);\r\n\r\n    return { success: true, summary: response.data.summary };\r\n  } catch (error) {\r\n    console.error(\"AI text summarization failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Send a freestyle prompt to AI with optional system prompt\r\n * @param prompt The main prompt for AI\r\n * @param systemPrompt Optional system prompt to control AI behavior\r\n * @returns AI response\r\n */\r\nexport async function freestyleAI(prompt: string, systemPrompt?: string) {\r\n  try {\r\n    const axiosInstance = createAxiosInstance();\r\n\r\n    const payload: any = { prompt };\r\n\r\n    if (systemPrompt) {\r\n      payload.systemPrompt = systemPrompt;\r\n    }\r\n\r\n    const response = await axiosInstance.post(\"/ai/freestyle\", payload);\r\n\r\n    return { success: true, response: response.data.response };\r\n  } catch (error) {\r\n    console.error(\"AI freestyle request failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA2EsB,gBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/ai.action.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createAxiosInstance } from \"@/lib/axios\";\r\n\r\n// Define interfaces for AI enhance feature\r\ninterface PreviousMessage {\r\n  content: string;\r\n  type: string;\r\n  senderId: string;\r\n  senderName: string;\r\n}\r\n\r\ninterface EnhanceMessageResponse {\r\n  enhancedMessage: string;\r\n}\r\n\r\n/**\r\n * Enhance a message using AI\r\n * @param message The message to enhance\r\n * @param previousMessages Optional array of previous messages for context\r\n * @returns Enhanced message\r\n */\r\nexport async function enhanceMessage(\r\n  message: string,\r\n  previousMessages?: PreviousMessage[],\r\n) {\r\n  try {\r\n    const axiosInstance = createAxiosInstance();\r\n    console.log(\"axiosInstance\", axiosInstance.defaults.baseURL);\r\n    const response = await axiosInstance.post(\"/ai/enhance\", {\r\n      message,\r\n      previousMessages,\r\n    });\r\n\r\n    const result = response.data as EnhanceMessageResponse;\r\n    return { success: true, enhancedMessage: result.enhancedMessage };\r\n  } catch (error) {\r\n    console.error(\"AI message enhancement failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Generate AI response based on a prompt\r\n * @param prompt The prompt for AI generation\r\n * @returns Generated AI response\r\n */\r\nexport async function generateAIResponse(prompt: string) {\r\n  try {\r\n    const axiosInstance = createAxiosInstance();\r\n\r\n    const response = await axiosInstance.post(\"/ai/generate\", {\r\n      prompt,\r\n    });\r\n\r\n    return { success: true, response: response.data.response };\r\n  } catch (error) {\r\n    console.error(\"AI response generation failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Summarize text using AI\r\n * @param text The text to summarize\r\n * @param maxLength Optional maximum length for the summary\r\n * @param previousMessages Optional array of previous messages for context\r\n * @returns Summarized text\r\n */\r\nexport async function summarizeText(\r\n  text: string,\r\n  maxLength?: number,\r\n  previousMessages?: PreviousMessage[],\r\n) {\r\n  try {\r\n    const axiosInstance = createAxiosInstance();\r\n\r\n    const payload: any = { text };\r\n\r\n    if (maxLength) {\r\n      payload.maxLength = maxLength.toString();\r\n    }\r\n\r\n    if (previousMessages) {\r\n      payload.previousMessages = previousMessages;\r\n    }\r\n\r\n    const response = await axiosInstance.post(\"/ai/summarize\", payload);\r\n\r\n    return { success: true, summary: response.data.summary };\r\n  } catch (error) {\r\n    console.error(\"AI text summarization failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Send a freestyle prompt to AI with optional system prompt\r\n * @param prompt The main prompt for AI\r\n * @param systemPrompt Optional system prompt to control AI behavior\r\n * @returns AI response\r\n */\r\nexport async function freestyleAI(prompt: string, systemPrompt?: string) {\r\n  try {\r\n    const axiosInstance = createAxiosInstance();\r\n\r\n    const payload: any = { prompt };\r\n\r\n    if (systemPrompt) {\r\n      payload.systemPrompt = systemPrompt;\r\n    }\r\n\r\n    const response = await axiosInstance.post(\"/ai/freestyle\", payload);\r\n\r\n    return { success: true, response: response.data.response };\r\n  } catch (error) {\r\n    console.error(\"AI freestyle request failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAsBsB,iBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 536, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/friend.action.ts"], "sourcesContent": ["\"use server\";\r\nimport axios from \"axios\";\r\nimport { createAxiosInstance } from \"@/lib/axios\";\r\n// No need to import Friend from @/types/base\r\nimport {\r\n  getCachedRelationship,\r\n  cacheRelationship,\r\n  removeCachedRelationship,\r\n  clearRelationshipCache,\r\n} from \"@/utils/relationshipCache\";\r\n\r\n// Define types based on the API response\r\ninterface UserInfo {\r\n  fullName: string;\r\n  profilePictureUrl: string;\r\n  statusMessage?: string;\r\n  lastSeen?: string;\r\n  gender?: string;\r\n  bio?: string;\r\n  dateOfBirth?: string;\r\n}\r\n\r\ninterface FriendInfo {\r\n  id: string;\r\n  email: string;\r\n  phoneNumber: string;\r\n  userInfo: UserInfo;\r\n}\r\n\r\ninterface FriendshipResponse {\r\n  friendshipId: string;\r\n  friend: FriendInfo;\r\n  since: string;\r\n}\r\n\r\ninterface FriendRequest {\r\n  id: string;\r\n  senderId: string;\r\n  receiverId: string;\r\n  status: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  sender: FriendInfo;\r\n  receiver: FriendInfo;\r\n  introduce?: string;\r\n}\r\n\r\n// Simplified Friend type for UI components\r\nexport interface SimpleFriend {\r\n  id: string;\r\n  fullName: string;\r\n  profilePictureUrl: string;\r\n  statusMessage?: string;\r\n  lastSeen?: string;\r\n  email?: string;\r\n  phoneNumber?: string;\r\n  gender?: string;\r\n  bio?: string;\r\n  dateOfBirth?: string;\r\n}\r\n\r\n// Define types based on the API response\r\n\r\ninterface FriendInfo {\r\n  id: string;\r\n  email: string;\r\n  phoneNumber: string;\r\n  userInfo: UserInfo;\r\n}\r\n\r\ninterface FriendshipResponse {\r\n  friendshipId: string;\r\n  friend: FriendInfo;\r\n  since: string;\r\n}\r\n\r\ninterface FriendRequest {\r\n  id: string;\r\n  senderId: string;\r\n  receiverId: string;\r\n  status: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  sender: FriendInfo;\r\n  receiver: FriendInfo;\r\n  introduce?: string;\r\n}\r\n\r\n// Simplified Friend type for UI components\r\nexport interface SimpleFriend {\r\n  id: string;\r\n  fullName: string;\r\n  profilePictureUrl: string;\r\n  statusMessage?: string;\r\n  lastSeen?: string;\r\n  email?: string;\r\n  phoneNumber?: string;\r\n  gender?: string;\r\n  bio?: string;\r\n  dateOfBirth?: string;\r\n}\r\n\r\n// Lấy danh sách bạn bè của người dùng hiện tại\r\nexport async function getFriendsList(token?: string) {\r\n  try {\r\n    console.log(\r\n      \"Token received in getFriendsList:\",\r\n      token ? `Token exists: ${token.substring(0, 10)}...` : \"No token\",\r\n    );\r\n    const serverAxios = createAxiosInstance(token);\r\n    console.log(\r\n      \"Authorization header:\",\r\n      serverAxios.defaults.headers.common[\"Authorization\"],\r\n    );\r\n    const response = await serverAxios.get(\"/friends/list\");\r\n    const friendships: FriendshipResponse[] = response.data;\r\n\r\n    // Transform the API response to the format expected by UI components\r\n    const friends: SimpleFriend[] = friendships.map((friendship) => ({\r\n      id: friendship.friend.id,\r\n      fullName: friendship.friend.userInfo.fullName,\r\n      profilePictureUrl: friendship.friend.userInfo.profilePictureUrl,\r\n      statusMessage: friendship.friend.userInfo.statusMessage,\r\n      lastSeen: friendship.friend.userInfo.lastSeen,\r\n      email: friendship.friend.email,\r\n      phoneNumber: friendship.friend.phoneNumber,\r\n      gender: friendship.friend.userInfo.gender,\r\n      bio: friendship.friend.userInfo.bio,\r\n      dateOfBirth: friendship.friend.userInfo.dateOfBirth,\r\n    }));\r\n\r\n    return { success: true, friends };\r\n  } catch (error) {\r\n    console.error(\"Get friends list failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Lấy danh sách lời mời kết bạn đã nhận\r\nexport async function getReceivedFriendRequests(token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.get(\"/friends/requests/received\");\r\n\r\n    // Log raw API response\r\n    console.log(\"Raw received friend requests:\", response.data);\r\n\r\n    // Transform the API response to the format expected by UI components\r\n    const requests = response.data.map((request: FriendRequest) => ({\r\n      id: request.id,\r\n      fullName: request.sender.userInfo.fullName,\r\n      profilePictureUrl: request.sender.userInfo.profilePictureUrl,\r\n      // Get the introduce message from the API response\r\n      message: request.introduce || \"\",\r\n      timeAgo: new Date(request.createdAt).toLocaleDateString(),\r\n      // Add senderId for fetching complete user data\r\n      senderId: request.sender.id,\r\n    }));\r\n\r\n    return { success: true, requests };\r\n  } catch (error) {\r\n    console.error(\"Get received friend requests failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Lấy danh sách lời mời kết bạn đã gửi\r\nexport async function getSentFriendRequests(token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.get(\"/friends/requests/sent\");\r\n\r\n    // Log raw API response\r\n    console.log(\"Raw sent friend requests:\", response.data);\r\n\r\n    // Transform the API response to the format expected by UI components\r\n    const requests = response.data.map((request: FriendRequest) => ({\r\n      id: request.id,\r\n      fullName: request.receiver.userInfo.fullName,\r\n      profilePictureUrl: request.receiver.userInfo.profilePictureUrl,\r\n      timeAgo: new Date(request.createdAt).toLocaleDateString(),\r\n      // Add receiverId for fetching complete user data\r\n      receiverId: request.receiver.id,\r\n    }));\r\n\r\n    return { success: true, requests };\r\n  } catch (error) {\r\n    console.error(\"Get sent friend requests failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Gửi lời mời kết bạn\r\nexport async function sendFriendRequest(\r\n  userId: string,\r\n  introduce?: string,\r\n  token?: string,\r\n) {\r\n  try {\r\n    console.log(\r\n      `Sending friend request to user ${userId} with token: ${!!token}`,\r\n    );\r\n    const serverAxios = createAxiosInstance(token);\r\n\r\n    // Tạo payload theo đúng format API yêu cầu\r\n    const payload: { receiverId: string; introduce?: string } = {\r\n      receiverId: userId,\r\n    };\r\n\r\n    // Thêm introduce nếu có\r\n    if (introduce && introduce.trim()) {\r\n      payload.introduce = introduce.trim();\r\n    }\r\n\r\n    console.log(\"Friend request payload:\", payload);\r\n    const response = await serverAxios.post(\"/friends/request\", payload);\r\n    console.log(\"Friend request response:\", response.data);\r\n\r\n    // Update relationship cache to reflect the new pending status\r\n    removeCachedRelationship(userId);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Send friend request failed:\", error);\r\n\r\n    // Log chi tiết hơn về lỗi\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n        headers: error.response.headers,\r\n      });\r\n\r\n      // Return specific error message based on status code\r\n      if (error.response.status === 401) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn cần đăng nhập để thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 403) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn không có quyền thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 404) {\r\n        return {\r\n          success: false,\r\n          error: \"Không tìm thấy người dùng\",\r\n        };\r\n      }\r\n\r\n      if (error.response.data?.message) {\r\n        return {\r\n          success: false,\r\n          error: error.response.data.message,\r\n        };\r\n      }\r\n    }\r\n\r\n    // Network errors or other errors\r\n    if (error instanceof Error) {\r\n      return {\r\n        success: false,\r\n        error: error.message,\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Phản hồi lời mời kết bạn (chấp nhận, từ chối, block)\r\nexport async function respondToFriendRequest(\r\n  requestId: string,\r\n  status: \"ACCEPTED\" | \"DECLINED\" | \"BLOCKED\",\r\n  token?: string,\r\n) {\r\n  try {\r\n    console.log(\r\n      `respondToFriendRequest: requestId=${requestId}, status=${status}, hasToken=${!!token}`,\r\n    );\r\n    const serverAxios = createAxiosInstance(token);\r\n    const payload = {\r\n      requestId,\r\n      status,\r\n    };\r\n    console.log(\"Request payload:\", payload);\r\n    const response = await serverAxios.put(\"/friends/respond\", payload);\r\n    console.log(\"API response:\", response.data);\r\n\r\n    // Clear all relationship caches since we don't know which user this affects\r\n    // This is a simple approach - a more sophisticated one would track which user's request this is\r\n    clearRelationshipCache();\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Respond to friend request failed:\", error);\r\n\r\n    // Log chi tiết hơn về lỗi\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n        headers: error.response.headers,\r\n      });\r\n\r\n      // Return specific error message based on status code\r\n      if (error.response.status === 401) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn cần đăng nhập để thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 403) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn không có quyền thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 404) {\r\n        return {\r\n          success: false,\r\n          error: \"Không tìm thấy lời mời kết bạn\",\r\n        };\r\n      }\r\n\r\n      if (error.response.data?.message) {\r\n        return {\r\n          success: false,\r\n          error: error.response.data.message,\r\n        };\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Chấp nhận lời mời kết bạn (wrapper function for backward compatibility)\r\nexport async function acceptFriendRequest(requestId: string, token?: string) {\r\n  return respondToFriendRequest(requestId, \"ACCEPTED\", token);\r\n}\r\n\r\n// Từ chối lời mời kết bạn (wrapper function for backward compatibility)\r\nexport async function rejectFriendRequest(requestId: string, token?: string) {\r\n  console.log(\r\n    \"rejectFriendRequest called with requestId:\",\r\n    requestId,\r\n    \"and token:\",\r\n    !!token,\r\n  );\r\n  const result = await respondToFriendRequest(requestId, \"DECLINED\", token);\r\n  console.log(\"respondToFriendRequest result:\", result);\r\n  return result;\r\n}\r\n\r\n// Xóa bạn bè\r\nexport async function removeFriend(friendId: string, token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.delete(`/friends/${friendId}`);\r\n\r\n    // Update relationship cache\r\n    removeCachedRelationship(friendId);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Remove friend failed:\", error);\r\n\r\n    // Log chi tiết hơn về lỗi\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n        headers: error.response.headers,\r\n      });\r\n\r\n      // Return specific error message based on status code\r\n      if (error.response.status === 401) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn cần đăng nhập để thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 403) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn không có quyền thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 404) {\r\n        return {\r\n          success: false,\r\n          error: \"Không tìm thấy người dùng\",\r\n        };\r\n      }\r\n\r\n      if (error.response.data?.message) {\r\n        return {\r\n          success: false,\r\n          error: error.response.data.message,\r\n        };\r\n      }\r\n    }\r\n\r\n    // Network errors or other errors\r\n    if (error instanceof Error) {\r\n      return {\r\n        success: false,\r\n        error: error.message,\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Hủy lời mời kết bạn đã gửi\r\nexport async function cancelFriendRequest(requestId: string, token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.delete(`/friends/request/${requestId}`);\r\n\r\n    // Clear all relationship caches since we don't know which user this affects\r\n    clearRelationshipCache();\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Cancel friend request failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Chặn người dùng\r\nexport async function blockUser(userId: string, token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.post(`/friends/block/${userId}`);\r\n\r\n    // Update relationship cache\r\n    removeCachedRelationship(userId);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Block user failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Bỏ chặn người dùng\r\nexport async function unblockUser(userId: string, token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.delete(`/friends/block/${userId}`);\r\n\r\n    // Update relationship cache\r\n    removeCachedRelationship(userId);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Unblock user failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Define type for blocked user response\r\ninterface BlockedUserResponse {\r\n  id: string;\r\n  senderId: string;\r\n  receiverId: string;\r\n  status: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  receiver: FriendInfo;\r\n}\r\n\r\n// Lấy danh sách người dùng đã chặn\r\nexport async function getBlockedUsers(token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.get(\"/friends/blocked\");\r\n\r\n    // Transform the API response to the format expected by UI components\r\n    const users: SimpleFriend[] = response.data.map(\r\n      (item: BlockedUserResponse) => ({\r\n        id: item.receiver.id,\r\n        fullName: item.receiver.userInfo.fullName,\r\n        profilePictureUrl: item.receiver.userInfo.profilePictureUrl,\r\n        email: item.receiver.email,\r\n        phoneNumber: item.receiver.phoneNumber,\r\n      }),\r\n    );\r\n\r\n    return { success: true, users };\r\n  } catch (error) {\r\n    console.error(\"Get blocked users failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Batch fetch relationships for multiple users is implemented here\r\n// The cache functions are imported from utils/relationshipCache.ts\r\n\r\n// Batch fetch relationships for multiple users\r\nexport async function batchGetRelationships(userIds: string[], token?: string) {\r\n  // Filter out duplicate IDs\r\n  const uniqueIds = [...new Set(userIds)];\r\n\r\n  // Check which relationships are already in cache\r\n  const cachedRelationships: Record<string, { status: string }> = {};\r\n  const idsToFetch: string[] = [];\r\n\r\n  uniqueIds.forEach((id) => {\r\n    const cachedData = getCachedRelationship(id);\r\n    if (cachedData) {\r\n      cachedRelationships[id] = cachedData;\r\n    } else {\r\n      idsToFetch.push(id);\r\n    }\r\n  });\r\n\r\n  // If all relationships are in cache, return immediately\r\n  if (idsToFetch.length === 0) {\r\n    console.log(`All ${uniqueIds.length} relationships found in cache`);\r\n    return { success: true, relationships: cachedRelationships };\r\n  }\r\n\r\n  // Otherwise, fetch the remaining relationships\r\n  try {\r\n    console.log(`Batch fetching ${idsToFetch.length} relationships`);\r\n\r\n    // Fetch each relationship individually (could be optimized with a batch API endpoint)\r\n    const fetchPromises = idsToFetch.map((id) => getRelationship(id, token));\r\n    const results = await Promise.all(fetchPromises);\r\n\r\n    // Process results\r\n    results.forEach((result, index) => {\r\n      if (result.success && result.data) {\r\n        const userId = idsToFetch[index];\r\n        cachedRelationships[userId] = result.data;\r\n      }\r\n    });\r\n\r\n    return { success: true, relationships: cachedRelationships };\r\n  } catch (error) {\r\n    console.error(\"Batch get relationships failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n      relationships: cachedRelationships, // Return any cached relationships we did find\r\n    };\r\n  }\r\n}\r\n\r\n// Lấy mối quan hệ với một người dùng cụ thể\r\nexport async function getRelationship(targetId: string, token?: string) {\r\n  try {\r\n    // Check if relationship data is in cache and still valid\r\n    const cachedData = getCachedRelationship(targetId);\r\n    if (cachedData) {\r\n      console.log(`Using cached relationship data for user ID: ${targetId}`);\r\n      return { success: true, data: cachedData };\r\n    }\r\n\r\n    // Sử dụng serverAxios để gửi token xác thực\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.get(`/friends/relationship/${targetId}`);\r\n    console.log(\"Relationship response:\", response.data);\r\n\r\n    // Store relationship data in cache\r\n    cacheRelationship(targetId, response.data);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Get relationship failed:\", error);\r\n\r\n    // Log chi tiết hơn về lỗi\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n        headers: error.response.headers,\r\n      });\r\n\r\n      // Return specific error message based on status code\r\n      if (error.response.status === 401) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn cần đăng nhập để thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 403) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn không có quyền thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 404) {\r\n        return {\r\n          success: false,\r\n          error: \"Không tìm thấy người dùng\",\r\n        };\r\n      }\r\n\r\n      if (error.response.data?.message) {\r\n        return {\r\n          success: false,\r\n          error: error.response.data.message,\r\n        };\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA8hBsB,wBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 549, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/app/%28protected%29/dashboard/chat/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useSearchParams } from \"next/navigation\";\r\nimport { useEffect, useState, useRef } from \"react\";\r\nimport ContactList from \"@/components/chat/ConverstationList\";\r\nimport ChatArea from \"@/components/chat/ChatArea\";\r\nimport ContactInfo from \"@/components/chat/ConverstationInfo\";\r\nimport GroupInfo from \"@/components/chat/GroupInfo\";\r\n// import ChatSocketHandler from \"@/components/chat/ChatSocketHandler\";\r\nimport { User, UserInfo } from \"@/types/base\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { useChatStore } from \"@/stores/chatStore\";\r\nimport { useConversationsStore } from \"@/stores/conversationsStore\";\r\nimport { getUserDataById } from \"@/actions/user.action\";\r\nimport { markAllMessagesAsRead } from \"@/actions/message.action\";\r\n\r\nexport default function ChatPage() {\r\n  const [isTabContentVisible, setIsTabContentVisible] = useState(true);\r\n  const [showContactInfo, setShowContactInfo] = useState(false);\r\n  const [isMobile, setIsMobile] = useState(false);\r\n  const [isTablet, setIsTablet] = useState(false);\r\n\r\n  // Get state from stores\r\n  const currentUser = useAuthStore((state) => state.user);\r\n  const {\r\n    selectedContact,\r\n    selectedGroup,\r\n    setSelectedGroup,\r\n    currentChatType,\r\n    setSelectedContact,\r\n  } = useChatStore();\r\n  const { loadConversations, markAsRead } = useConversationsStore();\r\n\r\n  // Get URL search params (currently unused but may be needed for future features)\r\n  const searchParams = useSearchParams();\r\n  // const groupIdParam = searchParams.get(\"groupId\");\r\n  // const userIdParam = searchParams.get(\"userId\");\r\n\r\n  // Use a ref to track if conversations have been loaded\r\n  const conversationsLoadedRef = useRef(false);\r\n\r\n  // Load conversations when component mounts\r\n  useEffect(() => {\r\n    if (currentUser?.id && !conversationsLoadedRef.current) {\r\n      console.log(\r\n        `[ChatPage] Loading conversations for user ${currentUser.id}`,\r\n      );\r\n      loadConversations(currentUser.id);\r\n      // The API now returns both user and group conversations\r\n      conversationsLoadedRef.current = true;\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [currentUser?.id]); // Remove loadConversations from dependencies as Zustand store functions are stable\r\n\r\n  // Handle URL parameters for opening specific chats\r\n  // useEffect(() => {\r\n  //   const handleUrlParams = async () => {\r\n  //     if (!currentUser?.id) return;\r\n\r\n  //     // Wait for conversations to load\r\n  //     await new Promise((resolve) => setTimeout(resolve, 1000));\r\n\r\n  //     const chatStore = useChatStore.getState();\r\n\r\n  //     // Open group chat if groupId is provided\r\n  //     if (groupIdParam) {\r\n  //       console.log(`Opening group chat with ID: ${groupIdParam}`);\r\n  //       await chatStore.openChat(groupIdParam, \"GROUP\");\r\n  //     }\r\n  //     // Open user chat if userId is provided\r\n  //     else if (userIdParam) {\r\n  //       console.log(`Opening user chat with ID: ${userIdParam}`);\r\n  //       await chatStore.openChat(userIdParam, \"USER\");\r\n  //     }\r\n  //   };\r\n\r\n  //   handleUrlParams();\r\n  // }, [groupIdParam, userIdParam, currentUser?.id]);\r\n\r\n  // Track if a chat is currently open\r\n  const isChatOpen = selectedContact !== null || selectedGroup !== null;\r\n\r\n  // Handle window resize for responsive layout\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      const width = window.innerWidth;\r\n      const newIsMobile = width < 768;\r\n      const newIsTablet = width >= 768 && width < 1024;\r\n      const newIsTabContentVisible = !isChatOpen || width >= 768;\r\n      const newShowContactInfo = width >= 1024 && showContactInfo;\r\n\r\n      // Only update states if values have changed\r\n      if (newIsMobile !== isMobile) {\r\n        setIsMobile(newIsMobile);\r\n      }\r\n      if (newIsTablet !== isTablet) {\r\n        setIsTablet(newIsTablet);\r\n      }\r\n      if (newIsTabContentVisible !== isTabContentVisible) {\r\n        setIsTabContentVisible(newIsTabContentVisible);\r\n      }\r\n      if (newShowContactInfo !== showContactInfo) {\r\n        setShowContactInfo(newShowContactInfo);\r\n      }\r\n    };\r\n\r\n    // Initial call\r\n    handleResize();\r\n\r\n    // Add event listener\r\n    window.addEventListener(\"resize\", handleResize);\r\n\r\n    // Cleanup\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [isChatOpen, showContactInfo]); // Only depend on these values\r\n\r\n  // Handle selecting a contact or group\r\n  const handleSelectContact = async (\r\n    id: string | null,\r\n    type: \"USER\" | \"GROUP\",\r\n  ) => {\r\n    console.log(`[ChatPage] Selecting ${type}: ${id}`);\r\n\r\n    if (!id) {\r\n      setSelectedContact(null);\r\n      setSelectedGroup(null);\r\n      return;\r\n    }\r\n\r\n    const chatStore = useChatStore.getState();\r\n    const conversationsStore = useConversationsStore.getState();\r\n\r\n    // Mark messages as read when selecting a conversation\r\n    if (type === \"USER\") {\r\n      // Clear any selected group when selecting a contact\r\n      setSelectedGroup(null);\r\n\r\n      // Check if this contact is already selected to prevent infinite loops\r\n      const currentSelectedContact = chatStore.selectedContact;\r\n      if (currentSelectedContact?.id === id) {\r\n        console.log(`[ChatPage] Contact ${id} is already selected, skipping`);\r\n        return;\r\n      }\r\n\r\n      // First, check if we already have this contact in our conversations store\r\n      const existingConversation = conversationsStore.conversations.find(\r\n        (conv) => conv.type === \"USER\" && conv.contact.id === id,\r\n      );\r\n\r\n      if (existingConversation) {\r\n        // Use the contact from the conversations store immediately\r\n        setSelectedContact(existingConversation.contact);\r\n\r\n        // Mark all messages as read using the store\r\n        conversationsStore.markAsRead(id);\r\n\r\n        // Check if we have cached messages before forcing a reload\r\n        const cacheKey = `USER_${id}`;\r\n        const cachedData = chatStore.messageCache[cacheKey];\r\n        const currentTime = new Date();\r\n        const isCacheValid =\r\n          cachedData &&\r\n          currentTime.getTime() - cachedData.lastFetched.getTime() <\r\n            5 * 60 * 1000;\r\n\r\n        // Only reload if we don't have valid cache\r\n        if (!isCacheValid || !cachedData || cachedData.messages.length === 0) {\r\n          console.log(\r\n            `[ChatPage] No valid cache for user ${id}, reloading messages`,\r\n          );\r\n          chatStore.setShouldFetchMessages(true);\r\n          chatStore.reloadConversationMessages(id, \"USER\");\r\n        }\r\n      } else {\r\n        try {\r\n          // Only fetch user data if we don't have it in the conversation store\r\n          const result = await getUserDataById(id);\r\n          if (result.success && result.user) {\r\n            // Ensure userInfo exists\r\n            const user = result.user;\r\n            if (!user.userInfo) {\r\n              user.userInfo = {\r\n                id: user.id,\r\n                fullName: user.email || user.phoneNumber || \"Unknown\",\r\n                profilePictureUrl: null,\r\n                statusMessage: \"No status\",\r\n                blockStrangers: false,\r\n                createdAt: new Date(),\r\n                updatedAt: new Date(),\r\n                userAuth: user,\r\n              };\r\n            }\r\n\r\n            // Only update the contact if it's still the selected one\r\n            const currentSelectedContact = chatStore.selectedContact;\r\n            if (!currentSelectedContact || currentSelectedContact.id !== id) {\r\n              setSelectedContact(user as User & { userInfo: UserInfo });\r\n              chatStore.setShouldFetchMessages(true);\r\n              chatStore.reloadConversationMessages(id, \"USER\");\r\n\r\n              // Mark all messages as read using the store\r\n              conversationsStore.markAsRead(id);\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error fetching user data:\", error);\r\n          setSelectedContact(null);\r\n        }\r\n      }\r\n    } else {\r\n      // Handle group conversation\r\n      try {\r\n        console.log(`[ChatPage] Opening group chat with ID: ${id}`);\r\n\r\n        // Check if socket is connected before opening group chat\r\n        const { messageSocket, isConnected } = window.messageSocket\r\n          ? {\r\n              messageSocket: window.messageSocket,\r\n              isConnected: window.messageSocket.connected,\r\n            }\r\n          : { messageSocket: null, isConnected: false };\r\n\r\n        if (messageSocket && !isConnected) {\r\n          console.log(\r\n            `[ChatPage] Socket not connected, attempting to reconnect before opening group chat`,\r\n          );\r\n          messageSocket.connect();\r\n          await new Promise((resolve) => setTimeout(resolve, 1000));\r\n        }\r\n\r\n        // Check if this group is already selected\r\n        const currentSelectedGroup = chatStore.selectedGroup;\r\n        if (currentSelectedGroup?.id === id) {\r\n          console.log(`[ChatPage] Group ${id} is already selected, skipping`);\r\n          return;\r\n        }\r\n\r\n        // Proceed with opening the chat\r\n        const success = await chatStore.openChat(id, \"GROUP\");\r\n\r\n        // Mark all messages as read using the store\r\n        conversationsStore.markAsRead(id);\r\n\r\n        // Only reload if the initial load failed or if there are no messages\r\n        // Reduced retry frequency to improve performance\r\n        if (!success) {\r\n          console.log(\r\n            `[ChatPage] Initial group chat load failed, will retry after delay`,\r\n          );\r\n          setTimeout(() => {\r\n            const currentSelectedGroup = chatStore.selectedGroup;\r\n            const currentMessages = chatStore.messages;\r\n            if (\r\n              currentSelectedGroup?.id === id &&\r\n              (!currentMessages || currentMessages.length === 0)\r\n            ) {\r\n              console.log(\r\n                `[ChatPage] Reloading group conversation messages after delay (no messages loaded)`,\r\n              );\r\n              chatStore.setShouldFetchMessages(true);\r\n              chatStore.reloadConversationMessages(id, \"GROUP\");\r\n            }\r\n          }, 2000); // Increased delay to reduce server load\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error opening group chat:\", error);\r\n        setSelectedGroup(null);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Note: Group selection is handled by handleSelectContact with type=\"GROUP\"\r\n\r\n  // Toggle contact info sidebar\r\n  const toggleContactInfo = () => {\r\n    setShowContactInfo((prev) => !prev);\r\n  };\r\n\r\n  // Function to go back to conversation list on mobile\r\n  const handleBackToList = () => {\r\n    setIsTabContentVisible(true);\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex h-full w-full bg-gray-100 overflow-hidden\">\r\n      {/* Left Sidebar - Contact List */}\r\n      <div\r\n        className={`w-full md:w-[340px] bg-white border-r flex flex-col overflow-hidden ${isTabContentVisible ? \"flex\" : \"hidden\"}`}\r\n      >\r\n        <ContactList\r\n          onSelectContact={(contactId) => {\r\n            handleSelectContact(contactId, \"USER\");\r\n            // Hide conversation list on mobile when selecting a chat\r\n            if (isMobile) {\r\n              setIsTabContentVisible(false);\r\n            }\r\n          }}\r\n          onSelectGroup={(groupId) => {\r\n            handleSelectContact(groupId, \"GROUP\");\r\n            // Hide conversation list on mobile when selecting a chat\r\n            if (isMobile) {\r\n              setIsTabContentVisible(false);\r\n            }\r\n          }}\r\n        />\r\n      </div>\r\n\r\n      {/* Main Content Area - Contains Chat and Info Panel */}\r\n      <div className=\"flex-1 flex overflow-hidden relative\">\r\n        {/* Main Chat Area */}\r\n        <div\r\n          className={`\r\n            ${isTabContentVisible && isMobile ? \"hidden\" : \"flex\"}\r\n            flex-col overflow-hidden transition-all duration-300\r\n            ${!isMobile && !isTablet && showContactInfo ? \"w-[calc(100%-340px)]\" : \"w-full\"}\r\n          `}\r\n        >\r\n          <ChatArea\r\n            currentUser={currentUser as User}\r\n            onToggleInfo={toggleContactInfo}\r\n            onBackToList={handleBackToList}\r\n          />\r\n        </div>\r\n\r\n        {/* Right Sidebar - Contact/Group Info */}\r\n        {/* On larger screens, it's a sidebar. On smaller screens, it overlays the chat area */}\r\n        <div\r\n          className={`\r\n            ${isMobile || isTablet ? \"absolute right-0 top-0 bottom-0 z-30\" : \"absolute right-0 top-0 bottom-0\"}\r\n            w-[340px] border-l bg-white flex flex-col overflow-hidden\r\n            transition-all duration-300 transform\r\n            ${showContactInfo ? \"translate-x-0 opacity-100\" : \"translate-x-full opacity-0 pointer-events-none\"}\r\n          `}\r\n        >\r\n          {/* Semi-transparent backdrop for mobile overlay mode */}\r\n          {(isMobile || isTablet) && (\r\n            <div\r\n              className={`fixed inset-0 bg-black/20 z-20 transition-opacity duration-300 ${showContactInfo ? \"opacity-100\" : \"opacity-0 pointer-events-none\"}`}\r\n              onClick={() => setShowContactInfo(false)}\r\n            />\r\n          )}\r\n\r\n          <div className=\"h-full relative z-30\">\r\n            {currentChatType === \"USER\" ? (\r\n              <ContactInfo\r\n                contact={\r\n                  selectedContact as (User & { userInfo: UserInfo }) | null\r\n                }\r\n                onClose={() => setShowContactInfo(false)}\r\n                isOverlay={isMobile || isTablet}\r\n              />\r\n            ) : (\r\n              <GroupInfo\r\n                group={selectedGroup}\r\n                onClose={() => setShowContactInfo(false)}\r\n                isOverlay={isMobile || isTablet}\r\n              />\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;AAee,SAAS;IACtB,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,wBAAwB;IACxB,MAAM,cAAc,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,IAAI;IACtD,MAAM,EACJ,eAAe,EACf,aAAa,EACb,gBAAgB,EAChB,eAAe,EACf,kBAAkB,EACnB,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IACf,MAAM,EAAE,iBAAiB,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,wBAAqB,AAAD;IAE9D,iFAAiF;IACjF,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,oDAAoD;IACpD,kDAAkD;IAElD,uDAAuD;IACvD,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEtC,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,MAAM,CAAC,uBAAuB,OAAO,EAAE;YACtD,QAAQ,GAAG,CACT,CAAC,0CAA0C,EAAE,YAAY,EAAE,EAAE;YAE/D,kBAAkB,YAAY,EAAE;YAChC,wDAAwD;YACxD,uBAAuB,OAAO,GAAG;QACnC;IACA,uDAAuD;IACzD,GAAG;QAAC,aAAa;KAAG,GAAG,mFAAmF;IAE1G,mDAAmD;IACnD,oBAAoB;IACpB,0CAA0C;IAC1C,oCAAoC;IAEpC,wCAAwC;IACxC,iEAAiE;IAEjE,iDAAiD;IAEjD,gDAAgD;IAChD,0BAA0B;IAC1B,oEAAoE;IACpE,yDAAyD;IACzD,QAAQ;IACR,8CAA8C;IAC9C,8BAA8B;IAC9B,kEAAkE;IAClE,uDAAuD;IACvD,QAAQ;IACR,OAAO;IAEP,uBAAuB;IACvB,oDAAoD;IAEpD,oCAAoC;IACpC,MAAM,aAAa,oBAAoB,QAAQ,kBAAkB;IAEjE,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,MAAM,QAAQ,OAAO,UAAU;YAC/B,MAAM,cAAc,QAAQ;YAC5B,MAAM,cAAc,SAAS,OAAO,QAAQ;YAC5C,MAAM,yBAAyB,CAAC,cAAc,SAAS;YACvD,MAAM,qBAAqB,SAAS,QAAQ;YAE5C,4CAA4C;YAC5C,IAAI,gBAAgB,UAAU;gBAC5B,YAAY;YACd;YACA,IAAI,gBAAgB,UAAU;gBAC5B,YAAY;YACd;YACA,IAAI,2BAA2B,qBAAqB;gBAClD,uBAAuB;YACzB;YACA,IAAI,uBAAuB,iBAAiB;gBAC1C,mBAAmB;YACrB;QACF;QAEA,eAAe;QACf;QAEA,qBAAqB;QACrB,OAAO,gBAAgB,CAAC,UAAU;QAElC,UAAU;QACV,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IAClD,uDAAuD;IACzD,GAAG;QAAC;QAAY;KAAgB,GAAG,8BAA8B;IAEjE,sCAAsC;IACtC,MAAM,sBAAsB,OAC1B,IACA;QAEA,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,KAAK,EAAE,EAAE,IAAI;QAEjD,IAAI,CAAC,IAAI;YACP,mBAAmB;YACnB,iBAAiB;YACjB;QACF;QAEA,MAAM,YAAY,0HAAA,CAAA,eAAY,CAAC,QAAQ;QACvC,MAAM,qBAAqB,mIAAA,CAAA,wBAAqB,CAAC,QAAQ;QAEzD,sDAAsD;QACtD,IAAI,SAAS,QAAQ;YACnB,oDAAoD;YACpD,iBAAiB;YAEjB,sEAAsE;YACtE,MAAM,yBAAyB,UAAU,eAAe;YACxD,IAAI,wBAAwB,OAAO,IAAI;gBACrC,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,GAAG,8BAA8B,CAAC;gBACpE;YACF;YAEA,0EAA0E;YAC1E,MAAM,uBAAuB,mBAAmB,aAAa,CAAC,IAAI,CAChE,CAAC,OAAS,KAAK,IAAI,KAAK,UAAU,KAAK,OAAO,CAAC,EAAE,KAAK;YAGxD,IAAI,sBAAsB;gBACxB,2DAA2D;gBAC3D,mBAAmB,qBAAqB,OAAO;gBAE/C,4CAA4C;gBAC5C,mBAAmB,UAAU,CAAC;gBAE9B,2DAA2D;gBAC3D,MAAM,WAAW,CAAC,KAAK,EAAE,IAAI;gBAC7B,MAAM,aAAa,UAAU,YAAY,CAAC,SAAS;gBACnD,MAAM,cAAc,IAAI;gBACxB,MAAM,eACJ,cACA,YAAY,OAAO,KAAK,WAAW,WAAW,CAAC,OAAO,KACpD,IAAI,KAAK;gBAEb,2CAA2C;gBAC3C,IAAI,CAAC,gBAAgB,CAAC,cAAc,WAAW,QAAQ,CAAC,MAAM,KAAK,GAAG;oBACpE,QAAQ,GAAG,CACT,CAAC,mCAAmC,EAAE,GAAG,oBAAoB,CAAC;oBAEhE,UAAU,sBAAsB,CAAC;oBACjC,UAAU,0BAA0B,CAAC,IAAI;gBAC3C;YACF,OAAO;gBACL,IAAI;oBACF,qEAAqE;oBACrE,MAAM,SAAS,MAAM,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD,EAAE;oBACrC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;wBACjC,yBAAyB;wBACzB,MAAM,OAAO,OAAO,IAAI;wBACxB,IAAI,CAAC,KAAK,QAAQ,EAAE;4BAClB,KAAK,QAAQ,GAAG;gCACd,IAAI,KAAK,EAAE;gCACX,UAAU,KAAK,KAAK,IAAI,KAAK,WAAW,IAAI;gCAC5C,mBAAmB;gCACnB,eAAe;gCACf,gBAAgB;gCAChB,WAAW,IAAI;gCACf,WAAW,IAAI;gCACf,UAAU;4BACZ;wBACF;wBAEA,yDAAyD;wBACzD,MAAM,yBAAyB,UAAU,eAAe;wBACxD,IAAI,CAAC,0BAA0B,uBAAuB,EAAE,KAAK,IAAI;4BAC/D,mBAAmB;4BACnB,UAAU,sBAAsB,CAAC;4BACjC,UAAU,0BAA0B,CAAC,IAAI;4BAEzC,4CAA4C;4BAC5C,mBAAmB,UAAU,CAAC;wBAChC;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,6BAA6B;oBAC3C,mBAAmB;gBACrB;YACF;QACF,OAAO;YACL,4BAA4B;YAC5B,IAAI;gBACF,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,IAAI;gBAE1D,yDAAyD;gBACzD,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,OAAO,aAAa,GACvD;oBACE,eAAe,OAAO,aAAa;oBACnC,aAAa,OAAO,aAAa,CAAC,SAAS;gBAC7C,IACA;oBAAE,eAAe;oBAAM,aAAa;gBAAM;gBAE9C,IAAI,iBAAiB,CAAC,aAAa;oBACjC,QAAQ,GAAG,CACT,CAAC,kFAAkF,CAAC;oBAEtF,cAAc,OAAO;oBACrB,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;gBACrD;gBAEA,0CAA0C;gBAC1C,MAAM,uBAAuB,UAAU,aAAa;gBACpD,IAAI,sBAAsB,OAAO,IAAI;oBACnC,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,GAAG,8BAA8B,CAAC;oBAClE;gBACF;gBAEA,gCAAgC;gBAChC,MAAM,UAAU,MAAM,UAAU,QAAQ,CAAC,IAAI;gBAE7C,4CAA4C;gBAC5C,mBAAmB,UAAU,CAAC;gBAE9B,qEAAqE;gBACrE,iDAAiD;gBACjD,IAAI,CAAC,SAAS;oBACZ,QAAQ,GAAG,CACT,CAAC,iEAAiE,CAAC;oBAErE,WAAW;wBACT,MAAM,uBAAuB,UAAU,aAAa;wBACpD,MAAM,kBAAkB,UAAU,QAAQ;wBAC1C,IACE,sBAAsB,OAAO,MAC7B,CAAC,CAAC,mBAAmB,gBAAgB,MAAM,KAAK,CAAC,GACjD;4BACA,QAAQ,GAAG,CACT,CAAC,iFAAiF,CAAC;4BAErF,UAAU,sBAAsB,CAAC;4BACjC,UAAU,0BAA0B,CAAC,IAAI;wBAC3C;oBACF,GAAG,OAAO,wCAAwC;gBACpD;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,iBAAiB;YACnB;QACF;IACF;IAEA,4EAA4E;IAE5E,8BAA8B;IAC9B,MAAM,oBAAoB;QACxB,mBAAmB,CAAC,OAAS,CAAC;IAChC;IAEA,qDAAqD;IACrD,MAAM,mBAAmB;QACvB,uBAAuB;IACzB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAW,CAAC,oEAAoE,EAAE,sBAAsB,SAAS,UAAU;0BAE3H,cAAA,8OAAC,+IAAA,CAAA,UAAW;oBACV,iBAAiB,CAAC;wBAChB,oBAAoB,WAAW;wBAC/B,yDAAyD;wBACzD,IAAI,UAAU;4BACZ,uBAAuB;wBACzB;oBACF;oBACA,eAAe,CAAC;wBACd,oBAAoB,SAAS;wBAC7B,yDAAyD;wBACzD,IAAI,UAAU;4BACZ,uBAAuB;wBACzB;oBACF;;;;;;;;;;;0BAKJ,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBACC,WAAW,CAAC;YACV,EAAE,uBAAuB,WAAW,WAAW,OAAO;;YAEtD,EAAE,CAAC,YAAY,CAAC,YAAY,kBAAkB,yBAAyB,SAAS;UAClF,CAAC;kCAED,cAAA,8OAAC,sIAAA,CAAA,UAAQ;4BACP,aAAa;4BACb,cAAc;4BACd,cAAc;;;;;;;;;;;kCAMlB,8OAAC;wBACC,WAAW,CAAC;YACV,EAAE,YAAY,WAAW,yCAAyC,kCAAkC;;;YAGpG,EAAE,kBAAkB,8BAA8B,iDAAiD;UACrG,CAAC;;4BAGA,CAAC,YAAY,QAAQ,mBACpB,8OAAC;gCACC,WAAW,CAAC,+DAA+D,EAAE,kBAAkB,gBAAgB,iCAAiC;gCAChJ,SAAS,IAAM,mBAAmB;;;;;;0CAItC,8OAAC;gCAAI,WAAU;0CACZ,oBAAoB,uBACnB,8OAAC,+IAAA,CAAA,UAAW;oCACV,SACE;oCAEF,SAAS,IAAM,mBAAmB;oCAClC,WAAW,YAAY;;;;;yDAGzB,8OAAC,uIAAA,CAAA,UAAS;oCACR,OAAO;oCACP,SAAS,IAAM,mBAAmB;oCAClC,WAAW,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvC", "debugId": null}}]}