{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/group/CreateGroupDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, use<PERSON><PERSON>back, useMemo } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>ontent,\r\n  Di<PERSON>Header,\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  DialogFooter,\r\n} from \"@/components/ui/dialog\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Users, Upload, Search } from \"lucide-react\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { createGroupWithAvatar } from \"@/actions/group.action\";\r\nimport { toast } from \"sonner\";\r\nimport { useFriendStore } from \"@/stores/friendStore\";\r\nimport { useConversationsStore } from \"@/stores/conversationsStore\";\r\nimport { Checkbox } from \"@/components/ui/checkbox\";\r\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\r\nimport { isEmail, isPhoneNumber } from \"@/utils/helpers\";\r\n\r\ninterface CreateGroupDialogProps {\r\n  isOpen: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  preSelectedFriendId?: string;\r\n}\r\n\r\nexport default function CreateGroupDialog({\r\n  isOpen,\r\n  onOpenChange,\r\n  preSelectedFriendId,\r\n}: CreateGroupDialogProps) {\r\n  const [groupName, setGroupName] = useState(\"\");\r\n  const [selectedFriends, setSelectedFriends] = useState<string[]>([]);\r\n  const [avatarFile, setAvatarFile] = useState<File | null>(null);\r\n  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const { user: currentUser } = useAuthStore();\r\n  const { friends } = useFriendStore();\r\n\r\n  // Memoize callback functions to prevent unnecessary re-renders\r\n  const handleAvatarChange = useCallback(\r\n    (e: React.ChangeEvent<HTMLInputElement>) => {\r\n      const file = e.target.files?.[0];\r\n      if (file) {\r\n        setAvatarFile(file);\r\n        const reader = new FileReader();\r\n        reader.onloadend = () => {\r\n          setAvatarPreview(reader.result as string);\r\n        };\r\n        reader.readAsDataURL(file);\r\n      }\r\n    },\r\n    [],\r\n  );\r\n\r\n  const handleFriendSelection = useCallback((friendId: string) => {\r\n    setSelectedFriends((prev) =>\r\n      prev.includes(friendId)\r\n        ? prev.filter((id) => id !== friendId)\r\n        : [...prev, friendId],\r\n    );\r\n  }, []);\r\n\r\n  const handleCreateGroup = useCallback(async () => {\r\n    if (!groupName.trim()) {\r\n      toast.error(\"Vui lòng nhập tên nhóm\");\r\n      return;\r\n    }\r\n\r\n    if (selectedFriends.length < 2) {\r\n      toast.error(\r\n        \"Vui lòng chọn ít nhất 2 thành viên (nhóm phải có tối thiểu 3 người kể cả bạn)\",\r\n      );\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    try {\r\n      // Chuyển đổi danh sách ID thành viên thành định dạng mới\r\n      const initialMembers = selectedFriends.map((userId) => ({\r\n        userId: userId,\r\n        // addedById sẽ được thêm tự động trong createGroupWithAvatar\r\n      }));\r\n\r\n      // Kiểm tra currentUser có tồn tại không\r\n      if (!currentUser || !currentUser.id) {\r\n        toast.error(\"Bạn cần đăng nhập để tạo nhóm\");\r\n        setIsLoading(false);\r\n        return;\r\n      }\r\n\r\n      // Gọi API tạo nhóm với avatar trong một lần gọi duy nhất\r\n      const result = await createGroupWithAvatar(\r\n        groupName.trim(),\r\n        currentUser.id,\r\n        initialMembers,\r\n        avatarFile || undefined,\r\n      );\r\n\r\n      if (result.success && result.group) {\r\n        // Đóng dialog trước\r\n        onOpenChange(false);\r\n\r\n        // Thông báo thành công\r\n        toast.success(\"Tạo nhóm thành công\");\r\n\r\n        // Reset form\r\n        setGroupName(\"\");\r\n        setSelectedFriends([]);\r\n        setAvatarFile(null);\r\n        setAvatarPreview(null);\r\n\r\n        // Backend sẽ tự động gửi socket events cho tất cả thành viên\r\n        // Chỉ cần reload conversations để cập nhật UI\r\n        if (currentUser?.id) {\r\n          console.log(\"Group created successfully:\", {\r\n            id: result.group.id,\r\n            name: result.group.name,\r\n            type: \"GROUP\",\r\n          });\r\n\r\n          // Reload conversations sau khi tạo nhóm thành công\r\n          setTimeout(() => {\r\n            const conversationsStore = useConversationsStore.getState();\r\n            conversationsStore.loadConversations(currentUser.id);\r\n          }, 500);\r\n        }\r\n      } else {\r\n        toast.error(result.error || \"Không thể tạo nhóm\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error creating group:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi tạo nhóm\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [groupName, selectedFriends, avatarFile, currentUser, onOpenChange]);\r\n\r\n  // State for search query\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n\r\n  // Pre-select friend if provided and reset when dialog opens/closes\r\n  useEffect(() => {\r\n    // Reset selected friends when dialog opens/closes\r\n    setSelectedFriends([]);\r\n\r\n    // Only add preSelectedFriendId if dialog is open\r\n    if (\r\n      isOpen &&\r\n      preSelectedFriendId &&\r\n      friends.some((friend) => friend.id === preSelectedFriendId)\r\n    ) {\r\n      setSelectedFriends([preSelectedFriendId]);\r\n    }\r\n  }, [isOpen, preSelectedFriendId, friends]);\r\n\r\n  // Use useMemo for filtered friends to avoid recalculating on every render\r\n  const filteredFriends = useMemo(() => {\r\n    if (!searchQuery.trim()) {\r\n      return friends;\r\n    }\r\n\r\n    // Check if search query is a phone number or email\r\n    const isPhone = isPhoneNumber(searchQuery);\r\n    const isEmailValue = isEmail(searchQuery);\r\n\r\n    // Filter friends based on search query\r\n    return friends.filter((friend) => {\r\n      // Search by phone number\r\n      if (isPhone && friend.phoneNumber) {\r\n        return friend.phoneNumber.includes(searchQuery);\r\n      }\r\n      // Search by email\r\n      if (isEmailValue && friend.email) {\r\n        return friend.email.toLowerCase().includes(searchQuery.toLowerCase());\r\n      }\r\n      // Search by name (default)\r\n      return friend.fullName.toLowerCase().includes(searchQuery.toLowerCase());\r\n    });\r\n  }, [searchQuery, friends]);\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"sm:max-w-[500px]\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"text-center text-lg font-semibold\">\r\n            Tạo nhóm\r\n          </DialogTitle>\r\n        </DialogHeader>\r\n\r\n        <div className=\"space-y-4 py-2\">\r\n          {/* Group avatar upload */}\r\n          <div className=\"flex flex-row w-full items-end justify-center\">\r\n            <div className=\"relative\">\r\n              <Avatar className=\"h-12 w-12 cursor-pointer\">\r\n                {avatarPreview ? (\r\n                  <AvatarImage src={avatarPreview} alt=\"Group avatar\" />\r\n                ) : (\r\n                  <>\r\n                    <AvatarFallback className=\"bg-gray-200\">\r\n                      <Users className=\"h-8 w-8 text-gray-400\" />\r\n                    </AvatarFallback>\r\n                  </>\r\n                )}\r\n              </Avatar>\r\n              <label\r\n                htmlFor=\"avatar-upload\"\r\n                className=\"absolute bottom-0 right-0 bg-blue-500 text-white p-1 rounded-full cursor-pointer\"\r\n              >\r\n                <Upload className=\"h-4 w-4\" />\r\n                <input\r\n                  id=\"avatar-upload\"\r\n                  type=\"file\"\r\n                  accept=\"image/*\"\r\n                  className=\"hidden\"\r\n                  onChange={handleAvatarChange}\r\n                />\r\n              </label>\r\n            </div>\r\n            {/* Group name input */}\r\n            <div className=\"ml-2 w-full border-b\">\r\n              <Input\r\n                value={groupName}\r\n                onChange={(e) => setGroupName(e.target.value)}\r\n                placeholder=\"Nhập tên nhóm...\"\r\n                className=\"w-full !border-none focus-visible:ring-0 focus-visible:ring-offset-0\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Search input */}\r\n          <div className=\"relative\">\r\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <Search className=\"h-4 w-4 text-gray-400\" />\r\n            </div>\r\n            <Input\r\n              type=\"text\"\r\n              placeholder=\"Nhập tên, số điện thoại, hoặc danh sách số điện thoại\"\r\n              className=\"pl-10 w-full text-xs\"\r\n              value={searchQuery}\r\n              onChange={(e) => setSearchQuery(e.target.value)}\r\n            />\r\n          </div>\r\n\r\n          {/* Friend selection */}\r\n          <div>\r\n            <div className=\"border rounded-md\">\r\n              <div className=\"p-2 border-b flex items-center justify-between\">\r\n                <span className=\"text-sm font-medium\">Trò chuyện gần đây</span>\r\n                <span className=\"text-sm text-blue-500\">\r\n                  Đã chọn: {selectedFriends.length}{\" \"}\r\n                  <span className=\"text-xs text-gray-500\">(tối thiểu 2)</span>\r\n                </span>\r\n              </div>\r\n\r\n              <ScrollArea className=\"h-[200px]\">\r\n                {filteredFriends.length > 0 ? (\r\n                  <div>\r\n                    {filteredFriends.map((friend) => (\r\n                      <div\r\n                        key={friend.id}\r\n                        className=\"flex items-center py-2 px-3 hover:bg-gray-50 border-b border-gray-100\"\r\n                      >\r\n                        <div className=\"flex items-center w-full\">\r\n                          <Checkbox\r\n                            id={`friend-${friend.id}`}\r\n                            checked={selectedFriends.includes(friend.id)}\r\n                            onCheckedChange={() =>\r\n                              handleFriendSelection(friend.id)\r\n                            }\r\n                            className=\"mr-3\"\r\n                          />\r\n                          <Avatar className=\"h-10 w-10 mr-3\">\r\n                            <AvatarImage\r\n                              src={friend.profilePictureUrl || undefined}\r\n                              alt={friend.fullName || \"\"}\r\n                            />\r\n                            <AvatarFallback>\r\n                              {friend.fullName?.charAt(0) || \"U\"}\r\n                            </AvatarFallback>\r\n                          </Avatar>\r\n                          <span className=\"text-sm font-medium\">\r\n                            {friend.fullName}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"p-4 text-center text-gray-500\">\r\n                    <p>Không tìm thấy kết quả</p>\r\n                  </div>\r\n                )}\r\n              </ScrollArea>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <DialogFooter>\r\n          <Button\r\n            variant=\"outline\"\r\n            onClick={() => onOpenChange(false)}\r\n            disabled={isLoading}\r\n            className=\"mr-2\"\r\n          >\r\n            Hủy\r\n          </Button>\r\n          <Button\r\n            onClick={handleCreateGroup}\r\n            disabled={\r\n              isLoading || !groupName.trim() || selectedFriends.length < 2\r\n            }\r\n            className=\"bg-blue-500 hover:bg-blue-600\"\r\n          >\r\n            {isLoading ? (\r\n              <>\r\n                <div className=\"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"></div>\r\n                Đang tạo...\r\n              </>\r\n            ) : (\r\n              \"Tạo nhóm\"\r\n            )}\r\n          </Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AArBA;;;;;;;;;;;;;;;;AA6Be,SAAS,kBAAkB,EACxC,MAAM,EACN,YAAY,EACZ,mBAAmB,EACI;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC1D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IACzC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD;IAEjC,+DAA+D;IAC/D,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACnC,CAAC;QACC,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAChC,IAAI,MAAM;YACR,cAAc;YACd,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS,GAAG;gBACjB,iBAAiB,OAAO,MAAM;YAChC;YACA,OAAO,aAAa,CAAC;QACvB;IACF,GACA,EAAE;IAGJ,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,mBAAmB,CAAC,OAClB,KAAK,QAAQ,CAAC,YACV,KAAK,MAAM,CAAC,CAAC,KAAO,OAAO,YAC3B;mBAAI;gBAAM;aAAS;IAE3B,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,IAAI,CAAC,UAAU,IAAI,IAAI;YACrB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT;YAEF;QACF;QAEA,aAAa;QACb,IAAI;YACF,yDAAyD;YACzD,MAAM,iBAAiB,gBAAgB,GAAG,CAAC,CAAC,SAAW,CAAC;oBACtD,QAAQ;gBAEV,CAAC;YAED,wCAAwC;YACxC,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,EAAE;gBACnC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,aAAa;gBACb;YACF;YAEA,yDAAyD;YACzD,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,wBAAqB,AAAD,EACvC,UAAU,IAAI,IACd,YAAY,EAAE,EACd,gBACA,cAAc;YAGhB,IAAI,OAAO,OAAO,IAAI,OAAO,KAAK,EAAE;gBAClC,oBAAoB;gBACpB,aAAa;gBAEb,uBAAuB;gBACvB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,aAAa;gBACb,aAAa;gBACb,mBAAmB,EAAE;gBACrB,cAAc;gBACd,iBAAiB;gBAEjB,6DAA6D;gBAC7D,8CAA8C;gBAC9C,IAAI,aAAa,IAAI;oBACnB,QAAQ,GAAG,CAAC,+BAA+B;wBACzC,IAAI,OAAO,KAAK,CAAC,EAAE;wBACnB,MAAM,OAAO,KAAK,CAAC,IAAI;wBACvB,MAAM;oBACR;oBAEA,mDAAmD;oBACnD,WAAW;wBACT,MAAM,qBAAqB,mIAAA,CAAA,wBAAqB,CAAC,QAAQ;wBACzD,mBAAmB,iBAAiB,CAAC,YAAY,EAAE;oBACrD,GAAG;gBACL;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC;QAAW;QAAiB;QAAY;QAAa;KAAa;IAEtE,yBAAyB;IACzB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,mEAAmE;IACnE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kDAAkD;QAClD,mBAAmB,EAAE;QAErB,iDAAiD;QACjD,IACE,UACA,uBACA,QAAQ,IAAI,CAAC,CAAC,SAAW,OAAO,EAAE,KAAK,sBACvC;YACA,mBAAmB;gBAAC;aAAoB;QAC1C;IACF,GAAG;QAAC;QAAQ;QAAqB;KAAQ;IAEzC,0EAA0E;IAC1E,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,OAAO;QACT;QAEA,mDAAmD;QACnD,MAAM,UAAU,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE;QAC9B,MAAM,eAAe,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD,EAAE;QAE7B,uCAAuC;QACvC,OAAO,QAAQ,MAAM,CAAC,CAAC;YACrB,yBAAyB;YACzB,IAAI,WAAW,OAAO,WAAW,EAAE;gBACjC,OAAO,OAAO,WAAW,CAAC,QAAQ,CAAC;YACrC;YACA,kBAAkB;YAClB,IAAI,gBAAgB,OAAO,KAAK,EAAE;gBAChC,OAAO,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;YACpE;YACA,2BAA2B;YAC3B,OAAO,OAAO,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QACvE;IACF,GAAG;QAAC;QAAa;KAAQ;IAEzB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;8BACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;wBAAC,WAAU;kCAAoC;;;;;;;;;;;8BAK7D,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;sDACf,8BACC,8OAAC,kIAAA,CAAA,cAAW;gDAAC,KAAK;gDAAe,KAAI;;;;;qEAErC;0DACE,cAAA,8OAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACxB,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAKzB,8OAAC;4CACC,SAAQ;4CACR,WAAU;;8DAEV,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,QAAO;oDACP,WAAU;oDACV,UAAU;;;;;;;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wCACJ,OAAO;wCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC5C,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;sCAMhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,8OAAC,iIAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,aAAY;oCACZ,WAAU;oCACV,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sCAKlD,8OAAC;sCACC,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;0DACtC,8OAAC;gDAAK,WAAU;;oDAAwB;oDAC5B,gBAAgB,MAAM;oDAAE;kEAClC,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAI5C,8OAAC,0IAAA,CAAA,aAAU;wCAAC,WAAU;kDACnB,gBAAgB,MAAM,GAAG,kBACxB,8OAAC;sDACE,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC;oDAEC,WAAU;8DAEV,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oIAAA,CAAA,WAAQ;gEACP,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;gEACzB,SAAS,gBAAgB,QAAQ,CAAC,OAAO,EAAE;gEAC3C,iBAAiB,IACf,sBAAsB,OAAO,EAAE;gEAEjC,WAAU;;;;;;0EAEZ,8OAAC,kIAAA,CAAA,SAAM;gEAAC,WAAU;;kFAChB,8OAAC,kIAAA,CAAA,cAAW;wEACV,KAAK,OAAO,iBAAiB,IAAI;wEACjC,KAAK,OAAO,QAAQ,IAAI;;;;;;kFAE1B,8OAAC,kIAAA,CAAA,iBAAc;kFACZ,OAAO,QAAQ,EAAE,OAAO,MAAM;;;;;;;;;;;;0EAGnC,8OAAC;gEAAK,WAAU;0EACb,OAAO,QAAQ;;;;;;;;;;;;mDAtBf,OAAO,EAAE;;;;;;;;;iEA6BpB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQf,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,aAAa;4BAC5B,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UACE,aAAa,CAAC,UAAU,IAAI,MAAM,gBAAgB,MAAM,GAAG;4BAE7D,WAAU;sCAET,0BACC;;kDACE,8OAAC;wCAAI,WAAU;;;;;;oCAA0F;;+CAI3G;;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/group/GroupChatHeaderSocketHandler.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from \"react\";\nimport { useAuthStore } from \"@/stores/authStore\";\nimport { useChatStore } from \"@/stores/chatStore\";\nimport { useConversationsStore } from \"@/stores/conversationsStore\";\nimport { useGroupSocket } from \"@/hooks/useGroupSocket\";\n\n// Define types for socket events\ninterface MemberAddedEventData {\n  groupId: string;\n  userId: string;\n  addedById: string;\n  timestamp: Date;\n}\n\ninterface MemberRemovedEventData {\n  groupId: string;\n  userId: string;\n  removedById: string;\n  timestamp: Date;\n}\n\ninterface MemberRoleUpdatedEventData {\n  groupId: string;\n  userId: string;\n  updatedById: string;\n  newRole: string;\n  timestamp: Date;\n}\n\ninterface GroupUpdatedEventData {\n  groupId: string;\n  updatedBy: string;\n  timestamp: Date;\n}\n\n/**\n * GroupChatHeaderSocketHandler component\n *\n * This component is responsible for handling socket events specifically for the GroupChatHeader component\n * It listens for group-related events and updates the member count and group info accordingly\n */\nconst GroupChatHeaderSocketHandler = ({\n  groupId,\n  onGroupUpdated,\n}: {\n  groupId: string;\n  onGroupUpdated?: () => void;\n}) => {\n  const currentUser = useAuthStore((state) => state.user);\n  const { socket } = useGroupSocket();\n  const { selectedGroup, refreshSelectedGroup } = useChatStore();\n  const { updateConversation } = useConversationsStore();\n\n  useEffect(() => {\n    if (!socket || !groupId) {\n      console.log(\n        \"[GroupChatHeaderSocketHandler] Socket or groupId not available, skipping event setup\",\n      );\n      return;\n    }\n\n    console.log(\n      `[GroupChatHeaderSocketHandler] Setting up socket event listeners for group ${groupId}`,\n    );\n\n    const handleGroupUpdated = (data: GroupUpdatedEventData) => {\n      if (data.groupId === groupId) {\n        console.log(\n          `[GroupChatHeaderSocketHandler] Group updated for ${groupId}, refreshing header data`,\n        );\n\n        // If this is the currently selected group, refresh it\n        if (selectedGroup && selectedGroup.id === groupId) {\n          refreshSelectedGroup();\n        }\n\n        // Call the onGroupUpdated callback if provided\n        if (onGroupUpdated) {\n          onGroupUpdated();\n        }\n\n        // Force update conversations to ensure header reflects changes\n        setTimeout(() => {\n          useConversationsStore.getState().forceUpdate();\n        }, 100);\n      }\n    };\n\n    const handleMemberAdded = (data: MemberAddedEventData) => {\n      if (data.groupId === groupId) {\n        console.log(\n          `[GroupChatHeaderSocketHandler] Member added to group ${groupId}, updating header`,\n        );\n\n        // If this is the currently selected group, refresh it\n        if (selectedGroup && selectedGroup.id === groupId) {\n          refreshSelectedGroup();\n        }\n\n        // Call the onGroupUpdated callback if provided\n        if (onGroupUpdated) {\n          onGroupUpdated();\n        }\n\n        // Force update conversations to ensure header reflects changes\n        setTimeout(() => {\n          useConversationsStore.getState().forceUpdate();\n        }, 100);\n      }\n    };\n\n    const handleMemberRemoved = (data: MemberRemovedEventData) => {\n      if (data.groupId === groupId) {\n        console.log(\n          `[GroupChatHeaderSocketHandler] Member removed from group ${groupId}, updating header`,\n        );\n\n        // Check if the current user was removed\n        if (data.userId === currentUser?.id) {\n          console.log(\n            `[GroupChatHeaderSocketHandler] Current user was removed from group ${groupId}`,\n          );\n          // The main GroupSocketHandler will handle this case\n          return;\n        }\n\n        // If this is the currently selected group, refresh it\n        if (selectedGroup && selectedGroup.id === groupId) {\n          refreshSelectedGroup();\n        }\n\n        // Call the onGroupUpdated callback if provided\n        if (onGroupUpdated) {\n          onGroupUpdated();\n        }\n\n        // Force update conversations to ensure header reflects changes\n        setTimeout(() => {\n          useConversationsStore.getState().forceUpdate();\n        }, 100);\n      }\n    };\n\n    const handleMemberRoleUpdated = (data: MemberRoleUpdatedEventData) => {\n      if (data.groupId === groupId) {\n        console.log(\n          `[GroupChatHeaderSocketHandler] Member role updated in group ${groupId}, updating header`,\n        );\n\n        // If this is the currently selected group, refresh it\n        if (selectedGroup && selectedGroup.id === groupId) {\n          refreshSelectedGroup();\n        }\n\n        // Call the onGroupUpdated callback if provided\n        if (onGroupUpdated) {\n          onGroupUpdated();\n        }\n\n        // Force update conversations to ensure header reflects changes\n        setTimeout(() => {\n          useConversationsStore.getState().forceUpdate();\n        }, 100);\n      }\n    };\n\n    // Register event listeners\n    socket.on(\"groupUpdated\", handleGroupUpdated);\n    socket.on(\"memberAdded\", handleMemberAdded);\n    socket.on(\"memberRemoved\", handleMemberRemoved);\n    socket.on(\"memberRoleUpdated\", handleMemberRoleUpdated);\n    socket.on(\"roleChanged\", handleMemberRoleUpdated); // Legacy event\n\n    // Cleanup on unmount\n    return () => {\n      console.log(\n        `[GroupChatHeaderSocketHandler] Cleaning up socket event listeners for group ${groupId}`,\n      );\n      socket.off(\"groupUpdated\", handleGroupUpdated);\n      socket.off(\"memberAdded\", handleMemberAdded);\n      socket.off(\"memberRemoved\", handleMemberRemoved);\n      socket.off(\"memberRoleUpdated\", handleMemberRoleUpdated);\n      socket.off(\"roleChanged\", handleMemberRoleUpdated);\n    };\n  }, [\n    socket,\n    groupId,\n    currentUser?.id,\n    selectedGroup,\n    refreshSelectedGroup,\n    updateConversation,\n    onGroupUpdated,\n  ]);\n\n  // This component doesn't render anything\n  return null;\n};\n\nexport default GroupChatHeaderSocketHandler;\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;AAqCA;;;;;CAKC,GACD,MAAM,+BAA+B,CAAC,EACpC,OAAO,EACP,cAAc,EAIf;IACC,MAAM,cAAc,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,IAAI;IACtD,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAChC,MAAM,EAAE,aAAa,EAAE,oBAAoB,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IAC3D,MAAM,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,wBAAqB,AAAD;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU,CAAC,SAAS;YACvB,QAAQ,GAAG,CACT;YAEF;QACF;QAEA,QAAQ,GAAG,CACT,CAAC,2EAA2E,EAAE,SAAS;QAGzF,MAAM,qBAAqB,CAAC;YAC1B,IAAI,KAAK,OAAO,KAAK,SAAS;gBAC5B,QAAQ,GAAG,CACT,CAAC,iDAAiD,EAAE,QAAQ,wBAAwB,CAAC;gBAGvF,sDAAsD;gBACtD,IAAI,iBAAiB,cAAc,EAAE,KAAK,SAAS;oBACjD;gBACF;gBAEA,+CAA+C;gBAC/C,IAAI,gBAAgB;oBAClB;gBACF;gBAEA,+DAA+D;gBAC/D,WAAW;oBACT,mIAAA,CAAA,wBAAqB,CAAC,QAAQ,GAAG,WAAW;gBAC9C,GAAG;YACL;QACF;QAEA,MAAM,oBAAoB,CAAC;YACzB,IAAI,KAAK,OAAO,KAAK,SAAS;gBAC5B,QAAQ,GAAG,CACT,CAAC,qDAAqD,EAAE,QAAQ,iBAAiB,CAAC;gBAGpF,sDAAsD;gBACtD,IAAI,iBAAiB,cAAc,EAAE,KAAK,SAAS;oBACjD;gBACF;gBAEA,+CAA+C;gBAC/C,IAAI,gBAAgB;oBAClB;gBACF;gBAEA,+DAA+D;gBAC/D,WAAW;oBACT,mIAAA,CAAA,wBAAqB,CAAC,QAAQ,GAAG,WAAW;gBAC9C,GAAG;YACL;QACF;QAEA,MAAM,sBAAsB,CAAC;YAC3B,IAAI,KAAK,OAAO,KAAK,SAAS;gBAC5B,QAAQ,GAAG,CACT,CAAC,yDAAyD,EAAE,QAAQ,iBAAiB,CAAC;gBAGxF,wCAAwC;gBACxC,IAAI,KAAK,MAAM,KAAK,aAAa,IAAI;oBACnC,QAAQ,GAAG,CACT,CAAC,mEAAmE,EAAE,SAAS;oBAEjF,oDAAoD;oBACpD;gBACF;gBAEA,sDAAsD;gBACtD,IAAI,iBAAiB,cAAc,EAAE,KAAK,SAAS;oBACjD;gBACF;gBAEA,+CAA+C;gBAC/C,IAAI,gBAAgB;oBAClB;gBACF;gBAEA,+DAA+D;gBAC/D,WAAW;oBACT,mIAAA,CAAA,wBAAqB,CAAC,QAAQ,GAAG,WAAW;gBAC9C,GAAG;YACL;QACF;QAEA,MAAM,0BAA0B,CAAC;YAC/B,IAAI,KAAK,OAAO,KAAK,SAAS;gBAC5B,QAAQ,GAAG,CACT,CAAC,4DAA4D,EAAE,QAAQ,iBAAiB,CAAC;gBAG3F,sDAAsD;gBACtD,IAAI,iBAAiB,cAAc,EAAE,KAAK,SAAS;oBACjD;gBACF;gBAEA,+CAA+C;gBAC/C,IAAI,gBAAgB;oBAClB;gBACF;gBAEA,+DAA+D;gBAC/D,WAAW;oBACT,mIAAA,CAAA,wBAAqB,CAAC,QAAQ,GAAG,WAAW;gBAC9C,GAAG;YACL;QACF;QAEA,2BAA2B;QAC3B,OAAO,EAAE,CAAC,gBAAgB;QAC1B,OAAO,EAAE,CAAC,eAAe;QACzB,OAAO,EAAE,CAAC,iBAAiB;QAC3B,OAAO,EAAE,CAAC,qBAAqB;QAC/B,OAAO,EAAE,CAAC,eAAe,0BAA0B,eAAe;QAElE,qBAAqB;QACrB,OAAO;YACL,QAAQ,GAAG,CACT,CAAC,4EAA4E,EAAE,SAAS;YAE1F,OAAO,GAAG,CAAC,gBAAgB;YAC3B,OAAO,GAAG,CAAC,eAAe;YAC1B,OAAO,GAAG,CAAC,iBAAiB;YAC5B,OAAO,GAAG,CAAC,qBAAqB;YAChC,OAAO,GAAG,CAAC,eAAe;QAC5B;IACF,GAAG;QACD;QACA;QACA,aAAa;QACb;QACA;QACA;QACA;KACD;IAED,yCAAyC;IACzC,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 662, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/group/GroupInfoSocketHandler.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect } from \"react\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { useChatStore } from \"@/stores/chatStore\";\r\nimport { useConversationsStore } from \"@/stores/conversationsStore\";\r\nimport { useGroupSocket } from \"@/hooks/useGroupSocket\";\r\n\r\n// Define interfaces for socket event data\r\ninterface GroupUpdatedEventData {\r\n  groupId: string;\r\n  updatedBy?: string;\r\n  timestamp?: Date;\r\n  [key: string]: any; // Allow for additional properties\r\n}\r\n\r\ninterface MemberEventData {\r\n  groupId: string;\r\n  userId: string;\r\n  addedById?: string;\r\n  removedById?: string;\r\n  timestamp?: Date;\r\n  [key: string]: any; // Allow for additional properties\r\n}\r\n\r\ninterface RoleChangedEventData {\r\n  groupId: string;\r\n  userId: string;\r\n  newRole?: string;\r\n  oldRole?: string;\r\n  updatedBy?: string;\r\n  timestamp?: Date;\r\n  [key: string]: any; // Allow for additional properties\r\n}\r\n\r\ninterface AvatarUpdatedEventData {\r\n  groupId: string;\r\n  updatedBy?: string;\r\n  avatarUrl?: string;\r\n  newAvatarUrl?: string;\r\n  timestamp?: Date;\r\n  [key: string]: any; // Allow for additional properties\r\n}\r\n\r\n/**\r\n * GroupInfoSocketHandler component\r\n *\r\n * This component is responsible for handling socket events specifically for the GroupInfo component\r\n * It listens for group-related events and updates the GroupInfo component accordingly\r\n */\r\nconst GroupInfoSocketHandler = ({\r\n  groupId,\r\n  onGroupUpdated,\r\n}: {\r\n  groupId: string;\r\n  onGroupUpdated?: () => void;\r\n}) => {\r\n  const currentUser = useAuthStore((state) => state.user);\r\n  const { socket, joinGroupRoom } = useGroupSocket();\r\n  const { selectedGroup, setSelectedGroup } = useChatStore();\r\n\r\n  // Join the group room when the component mounts or groupId changes\r\n  useEffect(() => {\r\n    if (groupId && socket) {\r\n      console.log(`[GroupInfoSocketHandler] Joining group room: ${groupId}`);\r\n      joinGroupRoom(groupId);\r\n\r\n      // We don't need to force update conversations here anymore\r\n      // The data should already be available in the cache\r\n    }\r\n  }, [groupId, socket, joinGroupRoom]);\r\n\r\n  // Listen for group-related events\r\n  useEffect(() => {\r\n    if (!socket || !groupId) return;\r\n\r\n    const handleGroupUpdated = (data: GroupUpdatedEventData) => {\r\n      if (data.groupId === groupId) {\r\n        console.log(\r\n          `[GroupInfoSocketHandler] Group ${groupId} updated, refreshing data`,\r\n        );\r\n\r\n        // Thêm throttle để tránh gọi onGroupUpdated quá thường xuyên\r\n        // Sử dụng biến toàn cục để theo dõi thời gian gọi cuối cùng\r\n        if (!window._lastGroupInfoUpdateTime) {\r\n          window._lastGroupInfoUpdateTime = {};\r\n        }\r\n\r\n        const now = Date.now();\r\n        const lastUpdateTime = window._lastGroupInfoUpdateTime[groupId] || 0;\r\n        const timeSinceLastUpdate = now - lastUpdateTime;\r\n\r\n        // Nếu đã gọi trong vòng 5 giây, bỏ qua để giảm lag\r\n        if (timeSinceLastUpdate < 5000) {\r\n          console.log(\r\n            `[GroupInfoSocketHandler] Skipping update, last update was ${timeSinceLastUpdate}ms ago`,\r\n          );\r\n          return;\r\n        }\r\n\r\n        // Cập nhật thời gian gọi cuối cùng\r\n        window._lastGroupInfoUpdateTime[groupId] = now;\r\n\r\n        // Call the onGroupUpdated callback if provided\r\n        // This will use the cache system to avoid redundant API calls\r\n        if (onGroupUpdated) {\r\n          onGroupUpdated();\r\n        }\r\n      }\r\n    };\r\n\r\n    const handleMemberAdded = (data: MemberEventData) => {\r\n      if (data.groupId === groupId) {\r\n        console.log(\r\n          `[GroupInfoSocketHandler] Member added to group ${groupId}, refreshing data`,\r\n        );\r\n\r\n        // Thêm throttle để tránh gọi onGroupUpdated quá thường xuyên\r\n        if (!window._lastGroupInfoUpdateTime) {\r\n          window._lastGroupInfoUpdateTime = {};\r\n        }\r\n\r\n        const now = Date.now();\r\n        const lastUpdateTime = window._lastGroupInfoUpdateTime[groupId] || 0;\r\n        const timeSinceLastUpdate = now - lastUpdateTime;\r\n\r\n        // Nếu đã gọi trong vòng 2 giây, bỏ qua\r\n        if (timeSinceLastUpdate < 2000) {\r\n          console.log(\r\n            `[GroupInfoSocketHandler] Skipping update, last update was ${timeSinceLastUpdate}ms ago`,\r\n          );\r\n          return;\r\n        }\r\n\r\n        // Cập nhật thời gian gọi cuối cùng\r\n        window._lastGroupInfoUpdateTime[groupId] = now;\r\n\r\n        // Call the onGroupUpdated callback if provided\r\n        // This will use the cache system to avoid redundant API calls\r\n        if (onGroupUpdated) {\r\n          onGroupUpdated();\r\n        }\r\n\r\n        // Force update conversations to ensure all components get updated\r\n        setTimeout(() => {\r\n          useConversationsStore.getState().forceUpdate();\r\n        }, 100);\r\n      }\r\n    };\r\n\r\n    const handleMemberRemoved = (data: MemberEventData) => {\r\n      if (data.groupId === groupId) {\r\n        console.log(\r\n          `[GroupInfoSocketHandler] Member removed from group ${groupId}, refreshing data`,\r\n        );\r\n\r\n        // Check if the current user was removed\r\n        if (data.userId === currentUser?.id) {\r\n          console.log(\r\n            `[GroupInfoSocketHandler] Current user was removed from group ${groupId}`,\r\n          );\r\n\r\n          // If this is the currently selected group, clear it\r\n          if (selectedGroup && selectedGroup.id === groupId) {\r\n            setSelectedGroup(null);\r\n          }\r\n\r\n          // Remove the group from conversations\r\n          useConversationsStore.getState().removeConversation(groupId);\r\n\r\n          // Force update conversations\r\n          setTimeout(() => {\r\n            useConversationsStore.getState().forceUpdate();\r\n          }, 100);\r\n        } else {\r\n          // Thêm throttle để tránh gọi onGroupUpdated quá thường xuyên\r\n          if (!window._lastGroupInfoUpdateTime) {\r\n            window._lastGroupInfoUpdateTime = {};\r\n          }\r\n\r\n          const now = Date.now();\r\n          const lastUpdateTime = window._lastGroupInfoUpdateTime[groupId] || 0;\r\n          const timeSinceLastUpdate = now - lastUpdateTime;\r\n\r\n          // Nếu đã gọi trong vòng 2 giây, bỏ qua\r\n          if (timeSinceLastUpdate < 2000) {\r\n            console.log(\r\n              `[GroupInfoSocketHandler] Skipping update, last update was ${timeSinceLastUpdate}ms ago`,\r\n            );\r\n            return;\r\n          }\r\n\r\n          // Cập nhật thời gian gọi cuối cùng\r\n          window._lastGroupInfoUpdateTime[groupId] = now;\r\n\r\n          // Call the onGroupUpdated callback if provided\r\n          // This will use the cache system to avoid redundant API calls\r\n          if (onGroupUpdated) {\r\n            onGroupUpdated();\r\n          }\r\n\r\n          // Force update conversations to ensure all components get updated\r\n          setTimeout(() => {\r\n            useConversationsStore.getState().forceUpdate();\r\n          }, 100);\r\n        }\r\n      }\r\n    };\r\n\r\n    const handleRoleChanged = (data: RoleChangedEventData) => {\r\n      if (data.groupId === groupId) {\r\n        console.log(\r\n          `[GroupInfoSocketHandler] Role changed in group ${groupId}, refreshing data`,\r\n        );\r\n\r\n        // Thêm throttle để tránh gọi onGroupUpdated quá thường xuyên\r\n        if (!window._lastGroupInfoUpdateTime) {\r\n          window._lastGroupInfoUpdateTime = {};\r\n        }\r\n\r\n        const now = Date.now();\r\n        const lastUpdateTime = window._lastGroupInfoUpdateTime[groupId] || 0;\r\n        const timeSinceLastUpdate = now - lastUpdateTime;\r\n\r\n        // Nếu đã gọi trong vòng 2 giây, bỏ qua\r\n        if (timeSinceLastUpdate < 2000) {\r\n          console.log(\r\n            `[GroupInfoSocketHandler] Skipping update, last update was ${timeSinceLastUpdate}ms ago`,\r\n          );\r\n          return;\r\n        }\r\n\r\n        // Cập nhật thời gian gọi cuối cùng\r\n        window._lastGroupInfoUpdateTime[groupId] = now;\r\n\r\n        // Call the onGroupUpdated callback if provided\r\n        // This will use the cache system to avoid redundant API calls\r\n        if (onGroupUpdated) {\r\n          onGroupUpdated();\r\n        }\r\n\r\n        // Force update conversations to ensure all components get updated\r\n        setTimeout(() => {\r\n          useConversationsStore.getState().forceUpdate();\r\n        }, 100);\r\n      }\r\n    };\r\n\r\n    const handleAvatarUpdated = (data: AvatarUpdatedEventData) => {\r\n      if (data.groupId === groupId) {\r\n        console.log(\r\n          `[GroupInfoSocketHandler] Avatar updated for group ${groupId}, refreshing data`,\r\n        );\r\n\r\n        // If we have avatarUrl in the data, update it directly in the selected group\r\n        if (data.avatarUrl && selectedGroup && selectedGroup.id === groupId) {\r\n          // Update the selected group directly\r\n          useChatStore.getState().setSelectedGroup({\r\n            ...selectedGroup,\r\n            avatarUrl: data.avatarUrl,\r\n          });\r\n\r\n          // Also update the cache\r\n          const chatStore = useChatStore.getState();\r\n          const cachedData = chatStore.groupCache\r\n            ? chatStore.groupCache[groupId]\r\n            : undefined;\r\n          if (cachedData) {\r\n            chatStore.groupCache[groupId] = {\r\n              ...cachedData,\r\n              group: {\r\n                ...cachedData.group,\r\n                avatarUrl: data.avatarUrl,\r\n              },\r\n            };\r\n          }\r\n        }\r\n\r\n        // Thêm throttle để tránh gọi onGroupUpdated quá thường xuyên\r\n        if (!window._lastGroupInfoUpdateTime) {\r\n          window._lastGroupInfoUpdateTime = {};\r\n        }\r\n\r\n        const now = Date.now();\r\n        const lastUpdateTime = window._lastGroupInfoUpdateTime[groupId] || 0;\r\n        const timeSinceLastUpdate = now - lastUpdateTime;\r\n\r\n        // Nếu đã gọi trong vòng 5 giây, bỏ qua để giảm lag\r\n        if (timeSinceLastUpdate < 5000) {\r\n          console.log(\r\n            `[GroupInfoSocketHandler] Skipping update, last update was ${timeSinceLastUpdate}ms ago`,\r\n          );\r\n          return;\r\n        }\r\n\r\n        // Cập nhật thời gian gọi cuối cùng\r\n        window._lastGroupInfoUpdateTime[groupId] = now;\r\n\r\n        // Call the onGroupUpdated callback if provided\r\n        // This will use the cache system to avoid redundant API calls\r\n        if (onGroupUpdated) {\r\n          onGroupUpdated();\r\n        }\r\n      }\r\n    };\r\n\r\n    // Register event listeners\r\n    socket.on(\"groupUpdated\", handleGroupUpdated);\r\n    socket.on(\"memberAdded\", handleMemberAdded);\r\n    socket.on(\"memberRemoved\", handleMemberRemoved);\r\n    socket.on(\"roleChanged\", handleRoleChanged);\r\n    socket.on(\"memberRoleUpdated\", handleRoleChanged); // Legacy event\r\n    socket.on(\"avatarUpdated\", handleAvatarUpdated);\r\n\r\n    // Cleanup on unmount\r\n    return () => {\r\n      socket.off(\"groupUpdated\", handleGroupUpdated);\r\n      socket.off(\"memberAdded\", handleMemberAdded);\r\n      socket.off(\"memberRemoved\", handleMemberRemoved);\r\n      socket.off(\"roleChanged\", handleRoleChanged);\r\n      socket.off(\"memberRoleUpdated\", handleRoleChanged);\r\n      socket.off(\"avatarUpdated\", handleAvatarUpdated);\r\n    };\r\n  }, [\r\n    socket,\r\n    groupId,\r\n    currentUser?.id,\r\n    selectedGroup,\r\n    setSelectedGroup,\r\n    onGroupUpdated,\r\n  ]);\r\n\r\n  // This component doesn't render anything\r\n  return null;\r\n};\r\n\r\nexport default GroupInfoSocketHandler;\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;AA4CA;;;;;CAKC,GACD,MAAM,yBAAyB,CAAC,EAC9B,OAAO,EACP,cAAc,EAIf;IACC,MAAM,cAAc,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,IAAI;IACtD,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAC/C,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IAEvD,mEAAmE;IACnE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,QAAQ;YACrB,QAAQ,GAAG,CAAC,CAAC,6CAA6C,EAAE,SAAS;YACrE,cAAc;QAEd,2DAA2D;QAC3D,oDAAoD;QACtD;IACF,GAAG;QAAC;QAAS;QAAQ;KAAc;IAEnC,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU,CAAC,SAAS;QAEzB,MAAM,qBAAqB,CAAC;YAC1B,IAAI,KAAK,OAAO,KAAK,SAAS;gBAC5B,QAAQ,GAAG,CACT,CAAC,+BAA+B,EAAE,QAAQ,yBAAyB,CAAC;gBAGtE,6DAA6D;gBAC7D,4DAA4D;gBAC5D,IAAI,CAAC,OAAO,wBAAwB,EAAE;oBACpC,OAAO,wBAAwB,GAAG,CAAC;gBACrC;gBAEA,MAAM,MAAM,KAAK,GAAG;gBACpB,MAAM,iBAAiB,OAAO,wBAAwB,CAAC,QAAQ,IAAI;gBACnE,MAAM,sBAAsB,MAAM;gBAElC,mDAAmD;gBACnD,IAAI,sBAAsB,MAAM;oBAC9B,QAAQ,GAAG,CACT,CAAC,0DAA0D,EAAE,oBAAoB,MAAM,CAAC;oBAE1F;gBACF;gBAEA,mCAAmC;gBACnC,OAAO,wBAAwB,CAAC,QAAQ,GAAG;gBAE3C,+CAA+C;gBAC/C,8DAA8D;gBAC9D,IAAI,gBAAgB;oBAClB;gBACF;YACF;QACF;QAEA,MAAM,oBAAoB,CAAC;YACzB,IAAI,KAAK,OAAO,KAAK,SAAS;gBAC5B,QAAQ,GAAG,CACT,CAAC,+CAA+C,EAAE,QAAQ,iBAAiB,CAAC;gBAG9E,6DAA6D;gBAC7D,IAAI,CAAC,OAAO,wBAAwB,EAAE;oBACpC,OAAO,wBAAwB,GAAG,CAAC;gBACrC;gBAEA,MAAM,MAAM,KAAK,GAAG;gBACpB,MAAM,iBAAiB,OAAO,wBAAwB,CAAC,QAAQ,IAAI;gBACnE,MAAM,sBAAsB,MAAM;gBAElC,uCAAuC;gBACvC,IAAI,sBAAsB,MAAM;oBAC9B,QAAQ,GAAG,CACT,CAAC,0DAA0D,EAAE,oBAAoB,MAAM,CAAC;oBAE1F;gBACF;gBAEA,mCAAmC;gBACnC,OAAO,wBAAwB,CAAC,QAAQ,GAAG;gBAE3C,+CAA+C;gBAC/C,8DAA8D;gBAC9D,IAAI,gBAAgB;oBAClB;gBACF;gBAEA,kEAAkE;gBAClE,WAAW;oBACT,mIAAA,CAAA,wBAAqB,CAAC,QAAQ,GAAG,WAAW;gBAC9C,GAAG;YACL;QACF;QAEA,MAAM,sBAAsB,CAAC;YAC3B,IAAI,KAAK,OAAO,KAAK,SAAS;gBAC5B,QAAQ,GAAG,CACT,CAAC,mDAAmD,EAAE,QAAQ,iBAAiB,CAAC;gBAGlF,wCAAwC;gBACxC,IAAI,KAAK,MAAM,KAAK,aAAa,IAAI;oBACnC,QAAQ,GAAG,CACT,CAAC,6DAA6D,EAAE,SAAS;oBAG3E,oDAAoD;oBACpD,IAAI,iBAAiB,cAAc,EAAE,KAAK,SAAS;wBACjD,iBAAiB;oBACnB;oBAEA,sCAAsC;oBACtC,mIAAA,CAAA,wBAAqB,CAAC,QAAQ,GAAG,kBAAkB,CAAC;oBAEpD,6BAA6B;oBAC7B,WAAW;wBACT,mIAAA,CAAA,wBAAqB,CAAC,QAAQ,GAAG,WAAW;oBAC9C,GAAG;gBACL,OAAO;oBACL,6DAA6D;oBAC7D,IAAI,CAAC,OAAO,wBAAwB,EAAE;wBACpC,OAAO,wBAAwB,GAAG,CAAC;oBACrC;oBAEA,MAAM,MAAM,KAAK,GAAG;oBACpB,MAAM,iBAAiB,OAAO,wBAAwB,CAAC,QAAQ,IAAI;oBACnE,MAAM,sBAAsB,MAAM;oBAElC,uCAAuC;oBACvC,IAAI,sBAAsB,MAAM;wBAC9B,QAAQ,GAAG,CACT,CAAC,0DAA0D,EAAE,oBAAoB,MAAM,CAAC;wBAE1F;oBACF;oBAEA,mCAAmC;oBACnC,OAAO,wBAAwB,CAAC,QAAQ,GAAG;oBAE3C,+CAA+C;oBAC/C,8DAA8D;oBAC9D,IAAI,gBAAgB;wBAClB;oBACF;oBAEA,kEAAkE;oBAClE,WAAW;wBACT,mIAAA,CAAA,wBAAqB,CAAC,QAAQ,GAAG,WAAW;oBAC9C,GAAG;gBACL;YACF;QACF;QAEA,MAAM,oBAAoB,CAAC;YACzB,IAAI,KAAK,OAAO,KAAK,SAAS;gBAC5B,QAAQ,GAAG,CACT,CAAC,+CAA+C,EAAE,QAAQ,iBAAiB,CAAC;gBAG9E,6DAA6D;gBAC7D,IAAI,CAAC,OAAO,wBAAwB,EAAE;oBACpC,OAAO,wBAAwB,GAAG,CAAC;gBACrC;gBAEA,MAAM,MAAM,KAAK,GAAG;gBACpB,MAAM,iBAAiB,OAAO,wBAAwB,CAAC,QAAQ,IAAI;gBACnE,MAAM,sBAAsB,MAAM;gBAElC,uCAAuC;gBACvC,IAAI,sBAAsB,MAAM;oBAC9B,QAAQ,GAAG,CACT,CAAC,0DAA0D,EAAE,oBAAoB,MAAM,CAAC;oBAE1F;gBACF;gBAEA,mCAAmC;gBACnC,OAAO,wBAAwB,CAAC,QAAQ,GAAG;gBAE3C,+CAA+C;gBAC/C,8DAA8D;gBAC9D,IAAI,gBAAgB;oBAClB;gBACF;gBAEA,kEAAkE;gBAClE,WAAW;oBACT,mIAAA,CAAA,wBAAqB,CAAC,QAAQ,GAAG,WAAW;gBAC9C,GAAG;YACL;QACF;QAEA,MAAM,sBAAsB,CAAC;YAC3B,IAAI,KAAK,OAAO,KAAK,SAAS;gBAC5B,QAAQ,GAAG,CACT,CAAC,kDAAkD,EAAE,QAAQ,iBAAiB,CAAC;gBAGjF,6EAA6E;gBAC7E,IAAI,KAAK,SAAS,IAAI,iBAAiB,cAAc,EAAE,KAAK,SAAS;oBACnE,qCAAqC;oBACrC,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,gBAAgB,CAAC;wBACvC,GAAG,aAAa;wBAChB,WAAW,KAAK,SAAS;oBAC3B;oBAEA,wBAAwB;oBACxB,MAAM,YAAY,0HAAA,CAAA,eAAY,CAAC,QAAQ;oBACvC,MAAM,aAAa,UAAU,UAAU,GACnC,UAAU,UAAU,CAAC,QAAQ,GAC7B;oBACJ,IAAI,YAAY;wBACd,UAAU,UAAU,CAAC,QAAQ,GAAG;4BAC9B,GAAG,UAAU;4BACb,OAAO;gCACL,GAAG,WAAW,KAAK;gCACnB,WAAW,KAAK,SAAS;4BAC3B;wBACF;oBACF;gBACF;gBAEA,6DAA6D;gBAC7D,IAAI,CAAC,OAAO,wBAAwB,EAAE;oBACpC,OAAO,wBAAwB,GAAG,CAAC;gBACrC;gBAEA,MAAM,MAAM,KAAK,GAAG;gBACpB,MAAM,iBAAiB,OAAO,wBAAwB,CAAC,QAAQ,IAAI;gBACnE,MAAM,sBAAsB,MAAM;gBAElC,mDAAmD;gBACnD,IAAI,sBAAsB,MAAM;oBAC9B,QAAQ,GAAG,CACT,CAAC,0DAA0D,EAAE,oBAAoB,MAAM,CAAC;oBAE1F;gBACF;gBAEA,mCAAmC;gBACnC,OAAO,wBAAwB,CAAC,QAAQ,GAAG;gBAE3C,+CAA+C;gBAC/C,8DAA8D;gBAC9D,IAAI,gBAAgB;oBAClB;gBACF;YACF;QACF;QAEA,2BAA2B;QAC3B,OAAO,EAAE,CAAC,gBAAgB;QAC1B,OAAO,EAAE,CAAC,eAAe;QACzB,OAAO,EAAE,CAAC,iBAAiB;QAC3B,OAAO,EAAE,CAAC,eAAe;QACzB,OAAO,EAAE,CAAC,qBAAqB,oBAAoB,eAAe;QAClE,OAAO,EAAE,CAAC,iBAAiB;QAE3B,qBAAqB;QACrB,OAAO;YACL,OAAO,GAAG,CAAC,gBAAgB;YAC3B,OAAO,GAAG,CAAC,eAAe;YAC1B,OAAO,GAAG,CAAC,iBAAiB;YAC5B,OAAO,GAAG,CAAC,eAAe;YAC1B,OAAO,GAAG,CAAC,qBAAqB;YAChC,OAAO,GAAG,CAAC,iBAAiB;QAC9B;IACF,GAAG;QACD;QACA;QACA,aAAa;QACb;QACA;QACA;KACD;IAED,yCAAyC;IACzC,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 903, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/group/AddMemberDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>ontent,\r\n  <PERSON><PERSON><PERSON>eader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Search, Check, UserCheck } from \"lucide-react\";\r\nimport { Checkbox } from \"@/components/ui/checkbox\";\r\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\r\nimport { useFriendStore } from \"@/stores/friendStore\";\r\nimport { addGroupMember, getGroupById } from \"@/actions/group.action\";\r\nimport { toast } from \"sonner\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { GroupRole } from \"@/types/base\";\r\n\r\ninterface AddMemberDialogProps {\r\n  groupId: string;\r\n  isOpen: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n}\r\n\r\nexport default function AddGroupMemberDialog({\r\n  groupId,\r\n  isOpen,\r\n  onOpenChange,\r\n}: AddMemberDialogProps) {\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [selectedFriends, setSelectedFriends] = useState<string[]>([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [existingMembers, setExistingMembers] = useState<string[]>([]);\r\n\r\n  const { friends, fetchFriends } = useFriendStore();\r\n  const currentUser = useAuthStore((state) => state.user);\r\n\r\n  // Load friends and group members when dialog opens\r\n  useEffect(() => {\r\n    if (isOpen && groupId) {\r\n      // Fetch friends\r\n      fetchFriends();\r\n      setSelectedFriends([]);\r\n      setSearchQuery(\"\");\r\n\r\n      // Fetch group to get existing members\r\n      const fetchGroupMembers = async () => {\r\n        try {\r\n          const result = await getGroupById(groupId);\r\n          if (result.success && result.group && result.group.members) {\r\n            // Extract member IDs\r\n            const memberIds = result.group.members.map(\r\n              (member: { userId: string }) => member.userId,\r\n            );\r\n            // const memberIds = result.group.members.map(member => member.userId);\r\n            setExistingMembers(memberIds);\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error fetching group members:\", error);\r\n        }\r\n      };\r\n\r\n      fetchGroupMembers();\r\n    }\r\n  }, [isOpen, groupId, fetchFriends]);\r\n\r\n  // Filter friends based on search query and active tab\r\n  const filteredFriends = friends.filter((friend) => {\r\n    const matchesSearch =\r\n      !searchQuery ||\r\n      (friend.fullName &&\r\n        friend.fullName.toLowerCase().includes(searchQuery.toLowerCase())) ||\r\n      (friend.email &&\r\n        friend.email.toLowerCase().includes(searchQuery.toLowerCase())) ||\r\n      (friend.phoneNumber && friend.phoneNumber.includes(searchQuery));\r\n    return matchesSearch;\r\n  });\r\n\r\n  // Toggle friend selection\r\n  const handleFriendSelection = (friendId: string) => {\r\n    setSelectedFriends((prev) =>\r\n      prev.includes(friendId)\r\n        ? prev.filter((id) => id !== friendId)\r\n        : [...prev, friendId],\r\n    );\r\n  };\r\n\r\n  // Add selected members to group\r\n  const handleAddMembers = async () => {\r\n    if (selectedFriends.length === 0) {\r\n      toast.error(\"Vui lòng chọn ít nhất một thành viên\");\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      // Add each selected friend to the group\r\n      for (const friendId of selectedFriends) {\r\n        const result = await addGroupMember(\r\n          groupId,\r\n          friendId,\r\n          currentUser?.id || \"\",\r\n          GroupRole.MEMBER,\r\n        );\r\n        if (!result.success) {\r\n          toast.error(`Không thể thêm thành viên: ${result.error}`);\r\n        }\r\n      }\r\n\r\n      toast.success(\"Đã thêm thành viên vào nhóm\");\r\n      onOpenChange(false);\r\n    } catch (error) {\r\n      console.error(\"Error adding members to group:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi thêm thành viên\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"sm:max-w-[500px] p-0\">\r\n        <DialogHeader className=\"px-4 py-2 border-b flex flex-row items-center justify-between\">\r\n          <DialogTitle className=\"text-base font-semibold\">\r\n            Thêm thành viên\r\n          </DialogTitle>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"h-8 w-8\"\r\n            onClick={() => onOpenChange(false)}\r\n          ></Button>\r\n        </DialogHeader>\r\n\r\n        <div className=\"p-4\">\r\n          <div className=\"relative mb-4\">\r\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <Search className=\"h-4 w-4 text-gray-400\" />\r\n            </div>\r\n            <Input\r\n              type=\"text\"\r\n              placeholder=\"Nhập tên, số điện thoại, hoặc danh sách số điện thoại\"\r\n              className=\"pl-10 w-full text-xs\"\r\n              value={searchQuery}\r\n              onChange={(e) => setSearchQuery(e.target.value)}\r\n            />\r\n          </div>\r\n\r\n          <ScrollArea className=\"h-[300px] pr-4\">\r\n            {filteredFriends.length > 0 ? (\r\n              <div className=\"space-y-1\">\r\n                {filteredFriends.map((friend) => (\r\n                  <div\r\n                    key={friend.id}\r\n                    className=\"flex items-center py-2 hover:bg-gray-50 rounded-md\"\r\n                  >\r\n                    {existingMembers.includes(friend.id) ? (\r\n                      <div className=\"ml-2 mr-3 h-4 w-4 flex items-center justify-center text-green-500\">\r\n                        <UserCheck className=\"h-4 w-4\" />\r\n                      </div>\r\n                    ) : (\r\n                      <Checkbox\r\n                        id={`friend-${friend.id}`}\r\n                        checked={selectedFriends.includes(friend.id)}\r\n                        onCheckedChange={() => handleFriendSelection(friend.id)}\r\n                        className=\"ml-2 mr-3\"\r\n                      />\r\n                    )}\r\n                    <Avatar className=\"h-10 w-10 mr-3\">\r\n                      <AvatarImage\r\n                        src={friend.profilePictureUrl || undefined}\r\n                        alt={friend.fullName || \"\"}\r\n                      />\r\n                      <AvatarFallback>\r\n                        {friend.fullName ? friend.fullName.charAt(0) : \"U\"}\r\n                      </AvatarFallback>\r\n                    </Avatar>\r\n                    <div className=\"flex-1\">\r\n                      <p className=\"text-sm font-medium\">{friend.fullName}</p>\r\n                      {existingMembers.includes(friend.id) && (\r\n                        <p className=\"text-xs text-green-500\">\r\n                          Đã là thành viên\r\n                        </p>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            ) : (\r\n              <div className=\"text-center py-8 text-gray-500\">\r\n                <p>Không tìm thấy kết quả</p>\r\n              </div>\r\n            )}\r\n          </ScrollArea>\r\n\r\n          <div className=\"flex justify-end gap-2 mt-4 pt-4 border-t\">\r\n            <Button\r\n              variant=\"outline\"\r\n              onClick={() => onOpenChange(false)}\r\n              disabled={isLoading}\r\n            >\r\n              Hủy\r\n            </Button>\r\n            <Button\r\n              onClick={handleAddMembers}\r\n              disabled={selectedFriends.length === 0 || isLoading}\r\n              className=\"bg-blue-500 hover:bg-blue-600 text-white\"\r\n            >\r\n              {isLoading ? (\r\n                <>\r\n                  <div className=\"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"></div>\r\n                  Đang xử lý...\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <Check className=\"h-4 w-4 mr-2\" />\r\n                  Xác nhận\r\n                </>\r\n              )}\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAMA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAnBA;;;;;;;;;;;;;;;AA2Be,SAAS,qBAAqB,EAC3C,OAAO,EACP,MAAM,EACN,YAAY,EACS;IACrB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEnE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD;IAC/C,MAAM,cAAc,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,IAAI;IAEtD,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,SAAS;YACrB,gBAAgB;YAChB;YACA,mBAAmB,EAAE;YACrB,eAAe;YAEf,sCAAsC;YACtC,MAAM,oBAAoB;gBACxB,IAAI;oBACF,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD,EAAE;oBAClC,IAAI,OAAO,OAAO,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,OAAO,EAAE;wBAC1D,qBAAqB;wBACrB,MAAM,YAAY,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,CACxC,CAAC,SAA+B,OAAO,MAAM;wBAE/C,uEAAuE;wBACvE,mBAAmB;oBACrB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,iCAAiC;gBACjD;YACF;YAEA;QACF;IACF,GAAG;QAAC;QAAQ;QAAS;KAAa;IAElC,sDAAsD;IACtD,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAC;QACtC,MAAM,gBACJ,CAAC,eACA,OAAO,QAAQ,IACd,OAAO,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC/D,OAAO,KAAK,IACX,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC5D,OAAO,WAAW,IAAI,OAAO,WAAW,CAAC,QAAQ,CAAC;QACrD,OAAO;IACT;IAEA,0BAA0B;IAC1B,MAAM,wBAAwB,CAAC;QAC7B,mBAAmB,CAAC,OAClB,KAAK,QAAQ,CAAC,YACV,KAAK,MAAM,CAAC,CAAC,KAAO,OAAO,YAC3B;mBAAI;gBAAM;aAAS;IAE3B;IAEA,gCAAgC;IAChC,MAAM,mBAAmB;QACvB,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,aAAa;QAEb,IAAI;YACF,wCAAwC;YACxC,KAAK,MAAM,YAAY,gBAAiB;gBACtC,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAChC,SACA,UACA,aAAa,MAAM,IACnB,oHAAA,CAAA,YAAS,CAAC,MAAM;gBAElB,IAAI,CAAC,OAAO,OAAO,EAAE;oBACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,2BAA2B,EAAE,OAAO,KAAK,EAAE;gBAC1D;YACF;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;sCAA0B;;;;;;sCAGjD,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,aAAa;;;;;;;;;;;;8BAIhC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,8OAAC,iIAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,aAAY;oCACZ,WAAU;oCACV,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sCAIlD,8OAAC,0IAAA,CAAA,aAAU;4BAAC,WAAU;sCACnB,gBAAgB,MAAM,GAAG,kBACxB,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC;wCAEC,WAAU;;4CAET,gBAAgB,QAAQ,CAAC,OAAO,EAAE,kBACjC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;qEAGvB,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;gDACzB,SAAS,gBAAgB,QAAQ,CAAC,OAAO,EAAE;gDAC3C,iBAAiB,IAAM,sBAAsB,OAAO,EAAE;gDACtD,WAAU;;;;;;0DAGd,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,8OAAC,kIAAA,CAAA,cAAW;wDACV,KAAK,OAAO,iBAAiB,IAAI;wDACjC,KAAK,OAAO,QAAQ,IAAI;;;;;;kEAE1B,8OAAC,kIAAA,CAAA,iBAAc;kEACZ,OAAO,QAAQ,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,KAAK;;;;;;;;;;;;0DAGnD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAuB,OAAO,QAAQ;;;;;;oDAClD,gBAAgB,QAAQ,CAAC,OAAO,EAAE,mBACjC,8OAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;;uCA3BrC,OAAO,EAAE;;;;;;;;;qDAoCpB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;8CAAE;;;;;;;;;;;;;;;;sCAKT,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,aAAa;oCAC5B,UAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,gBAAgB,MAAM,KAAK,KAAK;oCAC1C,WAAU;8CAET,0BACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;4CAA0F;;qEAI3G;;0DACE,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpD", "debugId": null}}, {"offset": {"line": 1261, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/group/GroupMemberList.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Group, User, GroupRole } from \"@/types/base\";\r\nimport {\r\n  ArrowLeft,\r\n  MoreHorizontal,\r\n  Shield,\r\n  UserMinus,\r\n  Ban,\r\n  UserPlus,\r\n  Link as LinkIcon,\r\n  Crown,\r\n} from \"lucide-react\";\r\nimport AddMemberDialog from \"./AddMemberDialog\";\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\r\nimport ProfileDialog from \"@/components/profile/ProfileDialog\";\r\nimport { batchGetUserData } from \"@/actions/user.action\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { batchGetRelationships } from \"@/actions/friend.action\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport { updateMemberRole, removeGroupMember } from \"@/actions/group.action\";\r\nimport { toast } from \"sonner\";\r\n\r\ninterface GroupMemberListProps {\r\n  group: Group | null;\r\n  isOpen: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  onBack: () => void;\r\n}\r\n\r\nexport default function GroupMemberList({\r\n  group,\r\n  isOpen,\r\n  onOpenChange,\r\n  onBack,\r\n}: GroupMemberListProps) {\r\n  const [selectedMember, setSelectedMember] = useState<User | null>(null);\r\n  const [showProfileDialog, setShowProfileDialog] = useState(false);\r\n  const [showFriendRequestForm, setShowFriendRequestForm] = useState(false);\r\n  const [showAddMemberDialog, setShowAddMemberDialog] = useState(false);\r\n  const [showKickDialog, setShowKickDialog] = useState(false);\r\n  const [memberToKick, setMemberToKick] = useState<string | null>(null);\r\n  const [showTransferLeadershipDialog, setShowTransferLeadershipDialog] = useState(false);\r\n  const [memberToTransfer, setMemberToTransfer] = useState<string | null>(null);\r\n  const [memberDetails, setMemberDetails] = useState<{ [key: string]: User }>(\r\n    {},\r\n  );\r\n  const [isProcessing, setIsProcessing] = useState(false);\r\n  const [adderDetails, setAdderDetails] = useState<{ [key: string]: User }>({});\r\n  const [relationships, setRelationships] = useState<{ [key: string]: string }>(\r\n    {},\r\n  );\r\n  const [isSendingRequest] = useState<{\r\n    [key: string]: boolean;\r\n  }>({});\r\n\r\n  const currentUser = useAuthStore((state) => state.user);\r\n\r\n  // Determine current user's role in the group\r\n  const currentUserRole =\r\n    group?.members?.find((member) => member.userId === currentUser?.id)?.role ||\r\n    \"MEMBER\";\r\n\r\n  // Fetch member details when group changes\r\n  useEffect(() => {\r\n    if (group?.id && group.members) {\r\n      const fetchMemberDetails = async () => {\r\n        const newMemberDetails: { [key: string]: User } = {};\r\n        const newAdderDetails: { [key: string]: User } = {};\r\n        const newRelationships: { [key: string]: string } = {};\r\n\r\n        try {\r\n          // Collect all user IDs that need to be fetched\r\n          const memberIds: string[] = [];\r\n          const adderIds: string[] = [];\r\n          const relationshipIds: string[] = [];\r\n\r\n          // Prepare lists of IDs to fetch\r\n          for (const member of group.members) {\r\n            // Check if we need to fetch user data\r\n            if (!member.user?.userInfo) {\r\n              memberIds.push(member.userId);\r\n            } else {\r\n              // If we already have the data, store it\r\n              newMemberDetails[member.userId] = member.user;\r\n            }\r\n\r\n            // Check if we need to fetch adder data\r\n            if (\r\n              member.addedBy &&\r\n              typeof member.addedBy === \"object\" &&\r\n              \"id\" in member.addedBy &&\r\n              \"fullName\" in member.addedBy\r\n            ) {\r\n              // Create a simple User object with the addedBy information\r\n              const adderInfo = member.addedBy as unknown as {\r\n                id: string;\r\n                fullName: string;\r\n              };\r\n              newAdderDetails[member.userId] = {\r\n                id: adderInfo.id,\r\n                userInfo: {\r\n                  id: adderInfo.id,\r\n                  fullName: adderInfo.fullName,\r\n                  blockStrangers: false,\r\n                  createdAt: new Date(),\r\n                  updatedAt: new Date(),\r\n                  userAuth: { id: adderInfo.id } as User,\r\n                },\r\n              } as unknown as User;\r\n            } else if (\r\n              member.addedById &&\r\n              member.addedById !== currentUser?.id &&\r\n              !member.addedBy\r\n            ) {\r\n              adderIds.push(member.addedById);\r\n            } else if (member.addedBy && \"userInfo\" in member.addedBy) {\r\n              newAdderDetails[member.userId] = member.addedBy as User;\r\n            }\r\n\r\n            // Check if we need to fetch relationship data\r\n            if (member.userId !== currentUser?.id) {\r\n              relationshipIds.push(member.userId);\r\n            }\r\n          }\r\n\r\n          // Batch fetch user data\r\n          if (memberIds.length > 0) {\r\n            console.log(`Batch fetching ${memberIds.length} member details`);\r\n            const userResult = await batchGetUserData(memberIds);\r\n            if (userResult.success && userResult.users) {\r\n              userResult.users.forEach((user) => {\r\n                newMemberDetails[user.id] = user;\r\n              });\r\n            }\r\n          }\r\n\r\n          // Batch fetch adder data\r\n          if (adderIds.length > 0) {\r\n            console.log(`Batch fetching ${adderIds.length} adder details`);\r\n            const adderResult = await batchGetUserData(adderIds);\r\n            if (adderResult.success && adderResult.users) {\r\n              // Match adders to members\r\n              for (const member of group.members) {\r\n                if (member.addedById) {\r\n                  const adder = adderResult.users.find(\r\n                    (u) => u.id === member.addedById,\r\n                  );\r\n                  if (adder) {\r\n                    newAdderDetails[member.userId] = adder;\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          // Batch fetch relationship data\r\n          if (relationshipIds.length > 0) {\r\n            console.log(\r\n              `Batch fetching ${relationshipIds.length} relationships`,\r\n            );\r\n            const accessToken =\r\n              useAuthStore.getState().accessToken || undefined;\r\n            const relationshipResult = await batchGetRelationships(\r\n              relationshipIds,\r\n              accessToken,\r\n            );\r\n\r\n            if (\r\n              relationshipResult.success &&\r\n              relationshipResult.relationships\r\n            ) {\r\n              // Process relationships\r\n              Object.entries(relationshipResult.relationships).forEach(\r\n                ([userId, data]) => {\r\n                  // Normalize relationship status\r\n                  const status = data.status || \"NONE\";\r\n\r\n                  // Standardize relationship values\r\n                  if (status === \"ACCEPTED\" || status === \"FRIEND\") {\r\n                    newRelationships[userId] = \"ACCEPTED\";\r\n                  } else if (status === \"PENDING_SENT\") {\r\n                    newRelationships[userId] = \"PENDING_SENT\";\r\n                  } else if (status === \"PENDING_RECEIVED\") {\r\n                    newRelationships[userId] = \"PENDING_RECEIVED\";\r\n                  } else {\r\n                    newRelationships[userId] = status;\r\n                  }\r\n\r\n                  console.log(\r\n                    `Normalized relationship with ${userId}:`,\r\n                    newRelationships[userId],\r\n                  );\r\n                },\r\n              );\r\n            }\r\n          }\r\n\r\n          // Set default relationship status for any members without data\r\n          for (const member of group.members) {\r\n            if (\r\n              member.userId !== currentUser?.id &&\r\n              !newRelationships[member.userId]\r\n            ) {\r\n              newRelationships[member.userId] = \"NONE\";\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error fetching member details:\", error);\r\n        }\r\n\r\n        // Update state with all the data we collected\r\n        setMemberDetails(newMemberDetails);\r\n        setAdderDetails(newAdderDetails);\r\n        setRelationships(newRelationships);\r\n      };\r\n\r\n      fetchMemberDetails();\r\n    }\r\n  }, [group?.id, group?.members, currentUser?.id]);\r\n\r\n  // Handle member click to show profile\r\n  const handleMemberClick = (memberId: string) => {\r\n    const memberData = memberDetails[memberId];\r\n    if (memberData) {\r\n      setSelectedMember(memberData);\r\n      setShowFriendRequestForm(false);\r\n      setShowProfileDialog(true);\r\n    }\r\n  };\r\n\r\n  // Handle send friend request\r\n  const handleSendFriendRequest = (userId: string) => {\r\n    const memberData = memberDetails[userId];\r\n    if (memberData) {\r\n      setSelectedMember(memberData);\r\n      setShowFriendRequestForm(true);\r\n      setShowProfileDialog(true);\r\n    }\r\n  };\r\n\r\n  // Handle promote member to co-leader\r\n  const handlePromoteMember = async (memberId: string) => {\r\n    if (!group?.id) return;\r\n    setIsProcessing(true);\r\n    try {\r\n      const result = await updateMemberRole(\r\n        group.id,\r\n        memberId,\r\n        GroupRole.CO_LEADER,\r\n      );\r\n      if (result.success) {\r\n        toast.success(\"Đã thăng cấp thành viên thành phó nhóm\");\r\n      } else {\r\n        toast.error(`Lỗi: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error promoting member:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi thăng cấp thành viên\");\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  };\r\n\r\n  // Handle demote co-leader to member\r\n  const handleDemoteMember = async (memberId: string) => {\r\n    if (!group?.id) return;\r\n    setIsProcessing(true);\r\n    try {\r\n      const result = await updateMemberRole(\r\n        group.id,\r\n        memberId,\r\n        GroupRole.MEMBER,\r\n      );\r\n      if (result.success) {\r\n        toast.success(\"Đã hạ cấp phó nhóm xuống thành viên thường\");\r\n      } else {\r\n        toast.error(`Lỗi: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error demoting member:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi hạ cấp thành viên\");\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  };\r\n\r\n  // Open kick member dialog\r\n  const openKickMemberDialog = (memberId: string) => {\r\n    setMemberToKick(memberId);\r\n    setShowKickDialog(true);\r\n  };\r\n\r\n  // Handle remove member from group\r\n  const handleKickMember = async () => {\r\n    if (!group?.id || !memberToKick) return;\r\n\r\n    setIsProcessing(true);\r\n    try {\r\n      const result = await removeGroupMember(group.id, memberToKick);\r\n      if (result.success) {\r\n        toast.success(\"Đã xóa thành viên khỏi nhóm\");\r\n        setShowKickDialog(false);\r\n      } else {\r\n        toast.error(`Lỗi: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error removing member:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi xóa thành viên\");\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  };\r\n\r\n  // Handle transfer leadership\r\n  const handleTransferLeadership = async () => {\r\n    if (!group?.id || !memberToTransfer) return;\r\n\r\n    setIsProcessing(true);\r\n    try {\r\n      const result = await updateMemberRole(\r\n        group.id,\r\n        memberToTransfer,\r\n        GroupRole.LEADER,\r\n      );\r\n      if (result.success) {\r\n        toast.success(\"Đã chuyển quyền trưởng nhóm thành công\");\r\n        setShowTransferLeadershipDialog(false);\r\n        setMemberToTransfer(null);\r\n      } else {\r\n        toast.error(`Lỗi: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error transferring leadership:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi chuyển quyền trưởng nhóm\");\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  };\r\n\r\n  // Open transfer leadership dialog\r\n  const openTransferLeadershipDialog = (memberId: string) => {\r\n    setMemberToTransfer(memberId);\r\n    setShowTransferLeadershipDialog(true);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Dialog\r\n        open={isOpen}\r\n        onOpenChange={(open) => {\r\n          // Only close the dialog if no other dialogs are open\r\n          if (!showKickDialog && !showProfileDialog && !showAddMemberDialog && !showTransferLeadershipDialog) {\r\n            onOpenChange(open);\r\n          }\r\n        }}\r\n      >\r\n        <DialogContent className=\"sm:max-w-[425px] h-auto !p-0 mt-0 mb-16 max-h-[90vh] overflow-y-auto no-scrollbar\">\r\n          <DialogHeader className=\"px-4 py-2 flex flex-row items-center border-b\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"mr-2 h-8 w-8\"\r\n              onClick={onBack}\r\n            >\r\n              <ArrowLeft className=\"h-4 w-4\" />\r\n            </Button>\r\n            <DialogTitle className=\"text-base font-semibold\">\r\n              Danh sách thành viên\r\n            </DialogTitle>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"ml-auto h-8 w-8\"\r\n              onClick={() => onOpenChange(false)}\r\n            ></Button>\r\n          </DialogHeader>\r\n\r\n          <div className=\"px-4 pb-4 border-b\">\r\n            <Button\r\n              className=\"w-full flex items-center justify-center bg-gray-100 hover:bg-gray-200 text-black\"\r\n              onClick={() => setShowAddMemberDialog(true)}\r\n            >\r\n              <UserPlus className=\"h-4 w-4\" />\r\n              <span>Thêm thành viên</span>\r\n            </Button>\r\n          </div>\r\n\r\n          <div className=\"px-4 flex justify-between items-center\">\r\n            <span className=\"text-sm\">\r\n              Danh sách thành viên ({group?.members?.length || 0})\r\n            </span>\r\n          </div>\r\n\r\n          <ScrollArea className=\"flex-1\">\r\n            {group?.members?.map((member) => {\r\n              const memberData = memberDetails[member.userId];\r\n              const initials = memberData?.userInfo?.fullName\r\n                ? memberData.userInfo.fullName.slice(0, 2).toUpperCase()\r\n                : \"??\";\r\n\r\n              return (\r\n                <div\r\n                  key={member.userId}\r\n                  className=\"flex items-center px-4 py-2 hover:bg-gray-100 justify-between\"\r\n                >\r\n                  <div\r\n                    className=\"flex items-center cursor-pointer\"\r\n                    onClick={() => handleMemberClick(member.userId)}\r\n                  >\r\n                    <Avatar className=\"h-10 w-10 mr-3\">\r\n                      <AvatarImage\r\n                        src={\r\n                          memberData?.userInfo?.profilePictureUrl || undefined\r\n                        }\r\n                        className=\"object-cover\"\r\n                      />\r\n                      <AvatarFallback className=\"bg-gray-200 text-gray-600\">\r\n                        {initials}\r\n                      </AvatarFallback>\r\n                    </Avatar>\r\n                    <div>\r\n                      <p className=\"font-medium\">\r\n                        {memberData?.userInfo?.fullName || \"Thành viên\"}\r\n                      </p>\r\n                      <p className=\"text-xs text-gray-500\">\r\n                        {member.role === \"LEADER\"\r\n                          ? \"Trưởng nhóm\"\r\n                          : member.role === \"CO_LEADER\"\r\n                            ? \"Phó nhóm\"\r\n                            : \"\"}\r\n                      </p>\r\n                      {member.userId !== currentUser?.id && (\r\n                        <p className=\"text-xs text-gray-500\">\r\n                          Thêm bởi{\" \"}\r\n                          {member.addedBy && \"fullName\" in member.addedBy\r\n                            ? (\r\n                                member.addedBy as unknown as {\r\n                                  fullName: string;\r\n                                }\r\n                              ).fullName\r\n                            : adderDetails[member.userId]?.userInfo?.fullName ||\r\n                              \"Người dùng\"}\r\n                        </p>\r\n                      )}\r\n                      {member.userId !== currentUser?.id &&\r\n                        member.userId !== group?.creatorId && (\r\n                          <p className=\"text-xs text-gray-500\">\r\n                            {member.joinedAt &&\r\n                              `Tham gia ${new Date(member.joinedAt).toLocaleDateString()}`}\r\n                          </p>\r\n                        )}\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center\">\r\n                    {/* Show pending status */}\r\n                    {member.userId !== currentUser?.id &&\r\n                      relationships[member.userId] === \"PENDING_SENT\" && (\r\n                        <Button\r\n                          variant=\"ghost\"\r\n                          size=\"icon\"\r\n                          disabled\r\n                          title=\"Đã gửi lời mời kết bạn\"\r\n                        >\r\n                          <LinkIcon className=\"h-4 w-4 text-gray-400\" />\r\n                        </Button>\r\n                      )}\r\n\r\n                    {/* Show dropdown menu for all members except current user */}\r\n                    {member.userId !== currentUser?.id && (\r\n                      <DropdownMenu>\r\n                        <DropdownMenuTrigger asChild>\r\n                          <Button variant=\"ghost\" size=\"icon\">\r\n                            <MoreHorizontal className=\"h-5 w-5\" />\r\n                          </Button>\r\n                        </DropdownMenuTrigger>\r\n                        <DropdownMenuContent align=\"end\">\r\n                          {/* Add friend option if not already friends */}\r\n                          {relationships[member.userId] === \"NONE\" && (\r\n                            <DropdownMenuItem\r\n                              onClick={() =>\r\n                                handleSendFriendRequest(member.userId)\r\n                              }\r\n                              disabled={isSendingRequest[member.userId]}\r\n                            >\r\n                              {isSendingRequest[member.userId] ? (\r\n                                <>\r\n                                  <div className=\"h-4 w-4 mr-2 rounded-full border-2 border-gray-600 border-t-transparent animate-spin\"></div>\r\n                                  Đang gửi...\r\n                                </>\r\n                              ) : (\r\n                                <>\r\n                                  <UserPlus className=\"h-4 w-4 mr-2 text-blue-500\" />\r\n                                  Kết bạn\r\n                                </>\r\n                              )}\r\n                            </DropdownMenuItem>\r\n                          )}\r\n\r\n                          {/* Leader/Co-leader management options */}\r\n                          {(currentUserRole === \"LEADER\" ||\r\n                            (currentUserRole === \"CO_LEADER\" &&\r\n                              member.role === \"MEMBER\")) && (\r\n                            <>\r\n                              {currentUserRole === \"LEADER\" &&\r\n                                member.role === \"MEMBER\" && (\r\n                                  <DropdownMenuItem\r\n                                    onClick={() =>\r\n                                      handlePromoteMember(member.userId)\r\n                                    }\r\n                                  >\r\n                                    <Shield className=\"h-4 w-4 mr-2\" />\r\n                                    Thăng phó nhóm\r\n                                  </DropdownMenuItem>\r\n                                )}\r\n                              {currentUserRole === \"LEADER\" &&\r\n                                member.role === \"CO_LEADER\" && (\r\n                                  <DropdownMenuItem\r\n                                    onClick={() =>\r\n                                      handleDemoteMember(member.userId)\r\n                                    }\r\n                                  >\r\n                                    <UserMinus className=\"h-4 w-4 mr-2\" />\r\n                                    Hạ xuống thành viên\r\n                                  </DropdownMenuItem>\r\n                                )}\r\n\r\n                              {/* Transfer leadership option - only for LEADER to other members */}\r\n                              {currentUserRole === \"LEADER\" &&\r\n                                member.role !== \"LEADER\" &&\r\n                                group?.members?.length > 1 && (\r\n                                  <DropdownMenuItem\r\n                                    onClick={() =>\r\n                                      openTransferLeadershipDialog(member.userId)\r\n                                    }\r\n                                    className=\"text-blue-500 focus:text-blue-500\"\r\n                                  >\r\n                                    <Crown className=\"h-4 w-4 mr-2\" />\r\n                                    Chuyển quyền trưởng nhóm\r\n                                  </DropdownMenuItem>\r\n                                )}\r\n\r\n                              <DropdownMenuSeparator />\r\n                              <DropdownMenuItem\r\n                                onClick={() =>\r\n                                  openKickMemberDialog(member.userId)\r\n                                }\r\n                                className=\"text-red-500 focus:text-red-500\"\r\n                              >\r\n                                <Ban className=\"h-4 w-4 mr-2\" />\r\n                                Xóa khỏi nhóm\r\n                              </DropdownMenuItem>\r\n                            </>\r\n                          )}\r\n                        </DropdownMenuContent>\r\n                      </DropdownMenu>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              );\r\n            })}\r\n          </ScrollArea>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Profile Dialog for members */}\r\n      {showProfileDialog && selectedMember && (\r\n        <ProfileDialog\r\n          user={selectedMember}\r\n          isOpen={showProfileDialog}\r\n          onOpenChange={(open) => {\r\n            setShowProfileDialog(open);\r\n            if (!open) {\r\n              setSelectedMember(null);\r\n              setShowFriendRequestForm(false);\r\n            }\r\n          }}\r\n          isOwnProfile={selectedMember.id === currentUser?.id}\r\n          initialShowFriendRequestForm={showFriendRequestForm}\r\n        />\r\n      )}\r\n\r\n      {/* Add Member Dialog */}\r\n      {group?.id && (\r\n        <AddMemberDialog\r\n          groupId={group.id}\r\n          isOpen={showAddMemberDialog}\r\n          onOpenChange={(open) => {\r\n            setShowAddMemberDialog(open);\r\n            // If the add member dialog is closed and the member list should still be open\r\n            if (!open && isOpen) {\r\n              // Force the member list to stay open\r\n              setTimeout(() => onOpenChange(true), 0);\r\n            }\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {/* Kick Member Confirmation Dialog */}\r\n      <AlertDialog\r\n        open={showKickDialog}\r\n        onOpenChange={(open) => {\r\n          setShowKickDialog(open);\r\n          // If the kick dialog is closed and the member list should still be open\r\n          if (!open && isOpen) {\r\n            // Force the member list to stay open\r\n            setTimeout(() => onOpenChange(true), 0);\r\n          }\r\n        }}\r\n      >\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Xóa thành viên</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Bạn có chắc chắn muốn xóa thành viên này khỏi nhóm? Họ sẽ không\r\n              thể xem tin nhắn trong nhóm này nữa.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={handleKickMember}\r\n              disabled={isProcessing}\r\n              className=\"bg-red-500 hover:bg-red-600\"\r\n            >\r\n              {isProcessing ? (\r\n                <>\r\n                  <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\r\n                  Đang xử lý...\r\n                </>\r\n              ) : (\r\n                \"Xóa thành viên\"\r\n              )}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n\r\n      {/* Transfer Leadership Confirmation Dialog */}\r\n      <AlertDialog\r\n        open={showTransferLeadershipDialog}\r\n        onOpenChange={(open) => {\r\n          setShowTransferLeadershipDialog(open);\r\n          // If the transfer dialog is closed and the member list should still be open\r\n          if (!open && isOpen) {\r\n            // Force the member list to stay open\r\n            setTimeout(() => onOpenChange(true), 0);\r\n          }\r\n        }}\r\n      >\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Chuyển quyền trưởng nhóm</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Người được chọn sẽ trở thành trưởng nhóm và có mọi quyền quản lý nhóm.\r\n              Bạn sẽ mất quyền quản lý nhưng vẫn là một thành viên của nhóm.\r\n              Hành động này không thể phục hồi.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel\r\n              disabled={isProcessing}\r\n              onClick={() => {\r\n                setShowTransferLeadershipDialog(false);\r\n                setMemberToTransfer(null);\r\n              }}\r\n            >\r\n              Hủy\r\n            </AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={handleTransferLeadership}\r\n              disabled={isProcessing}\r\n              className=\"bg-blue-500 hover:bg-blue-600\"\r\n            >\r\n              {isProcessing ? (\r\n                <>\r\n                  <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\r\n                  Đang xử lý...\r\n                </>\r\n              ) : (\r\n                \"Chuyển quyền\"\r\n              )}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AAUA;AACA;AACA;AACA;AACA;AACA;AAOA;AAMA;AACA;AA9CA;;;;;;;;;;;;;;;;;;AAuDe,SAAS,gBAAgB,EACtC,KAAK,EACL,MAAM,EACN,YAAY,EACZ,MAAM,EACe;IACrB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAClE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,8BAA8B,gCAAgC,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC/C,CAAC;IAEH,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAC3E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC/C,CAAC;IAEH,MAAM,CAAC,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAE/B,CAAC;IAEJ,MAAM,cAAc,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,IAAI;IAEtD,6CAA6C;IAC7C,MAAM,kBACJ,OAAO,SAAS,KAAK,CAAC,SAAW,OAAO,MAAM,KAAK,aAAa,KAAK,QACrE;IAEF,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO,MAAM,MAAM,OAAO,EAAE;YAC9B,MAAM,qBAAqB;gBACzB,MAAM,mBAA4C,CAAC;gBACnD,MAAM,kBAA2C,CAAC;gBAClD,MAAM,mBAA8C,CAAC;gBAErD,IAAI;oBACF,+CAA+C;oBAC/C,MAAM,YAAsB,EAAE;oBAC9B,MAAM,WAAqB,EAAE;oBAC7B,MAAM,kBAA4B,EAAE;oBAEpC,gCAAgC;oBAChC,KAAK,MAAM,UAAU,MAAM,OAAO,CAAE;wBAClC,sCAAsC;wBACtC,IAAI,CAAC,OAAO,IAAI,EAAE,UAAU;4BAC1B,UAAU,IAAI,CAAC,OAAO,MAAM;wBAC9B,OAAO;4BACL,wCAAwC;4BACxC,gBAAgB,CAAC,OAAO,MAAM,CAAC,GAAG,OAAO,IAAI;wBAC/C;wBAEA,uCAAuC;wBACvC,IACE,OAAO,OAAO,IACd,OAAO,OAAO,OAAO,KAAK,YAC1B,QAAQ,OAAO,OAAO,IACtB,cAAc,OAAO,OAAO,EAC5B;4BACA,2DAA2D;4BAC3D,MAAM,YAAY,OAAO,OAAO;4BAIhC,eAAe,CAAC,OAAO,MAAM,CAAC,GAAG;gCAC/B,IAAI,UAAU,EAAE;gCAChB,UAAU;oCACR,IAAI,UAAU,EAAE;oCAChB,UAAU,UAAU,QAAQ;oCAC5B,gBAAgB;oCAChB,WAAW,IAAI;oCACf,WAAW,IAAI;oCACf,UAAU;wCAAE,IAAI,UAAU,EAAE;oCAAC;gCAC/B;4BACF;wBACF,OAAO,IACL,OAAO,SAAS,IAChB,OAAO,SAAS,KAAK,aAAa,MAClC,CAAC,OAAO,OAAO,EACf;4BACA,SAAS,IAAI,CAAC,OAAO,SAAS;wBAChC,OAAO,IAAI,OAAO,OAAO,IAAI,cAAc,OAAO,OAAO,EAAE;4BACzD,eAAe,CAAC,OAAO,MAAM,CAAC,GAAG,OAAO,OAAO;wBACjD;wBAEA,8CAA8C;wBAC9C,IAAI,OAAO,MAAM,KAAK,aAAa,IAAI;4BACrC,gBAAgB,IAAI,CAAC,OAAO,MAAM;wBACpC;oBACF;oBAEA,wBAAwB;oBACxB,IAAI,UAAU,MAAM,GAAG,GAAG;wBACxB,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,UAAU,MAAM,CAAC,eAAe,CAAC;wBAC/D,MAAM,aAAa,MAAM,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE;wBAC1C,IAAI,WAAW,OAAO,IAAI,WAAW,KAAK,EAAE;4BAC1C,WAAW,KAAK,CAAC,OAAO,CAAC,CAAC;gCACxB,gBAAgB,CAAC,KAAK,EAAE,CAAC,GAAG;4BAC9B;wBACF;oBACF;oBAEA,yBAAyB;oBACzB,IAAI,SAAS,MAAM,GAAG,GAAG;wBACvB,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,SAAS,MAAM,CAAC,cAAc,CAAC;wBAC7D,MAAM,cAAc,MAAM,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE;wBAC3C,IAAI,YAAY,OAAO,IAAI,YAAY,KAAK,EAAE;4BAC5C,0BAA0B;4BAC1B,KAAK,MAAM,UAAU,MAAM,OAAO,CAAE;gCAClC,IAAI,OAAO,SAAS,EAAE;oCACpB,MAAM,QAAQ,YAAY,KAAK,CAAC,IAAI,CAClC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,SAAS;oCAElC,IAAI,OAAO;wCACT,eAAe,CAAC,OAAO,MAAM,CAAC,GAAG;oCACnC;gCACF;4BACF;wBACF;oBACF;oBAEA,gCAAgC;oBAChC,IAAI,gBAAgB,MAAM,GAAG,GAAG;wBAC9B,QAAQ,GAAG,CACT,CAAC,eAAe,EAAE,gBAAgB,MAAM,CAAC,cAAc,CAAC;wBAE1D,MAAM,cACJ,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,WAAW,IAAI;wBACzC,MAAM,qBAAqB,MAAM,CAAA,GAAA,sJAAA,CAAA,wBAAqB,AAAD,EACnD,iBACA;wBAGF,IACE,mBAAmB,OAAO,IAC1B,mBAAmB,aAAa,EAChC;4BACA,wBAAwB;4BACxB,OAAO,OAAO,CAAC,mBAAmB,aAAa,EAAE,OAAO,CACtD,CAAC,CAAC,QAAQ,KAAK;gCACb,gCAAgC;gCAChC,MAAM,SAAS,KAAK,MAAM,IAAI;gCAE9B,kCAAkC;gCAClC,IAAI,WAAW,cAAc,WAAW,UAAU;oCAChD,gBAAgB,CAAC,OAAO,GAAG;gCAC7B,OAAO,IAAI,WAAW,gBAAgB;oCACpC,gBAAgB,CAAC,OAAO,GAAG;gCAC7B,OAAO,IAAI,WAAW,oBAAoB;oCACxC,gBAAgB,CAAC,OAAO,GAAG;gCAC7B,OAAO;oCACL,gBAAgB,CAAC,OAAO,GAAG;gCAC7B;gCAEA,QAAQ,GAAG,CACT,CAAC,6BAA6B,EAAE,OAAO,CAAC,CAAC,EACzC,gBAAgB,CAAC,OAAO;4BAE5B;wBAEJ;oBACF;oBAEA,+DAA+D;oBAC/D,KAAK,MAAM,UAAU,MAAM,OAAO,CAAE;wBAClC,IACE,OAAO,MAAM,KAAK,aAAa,MAC/B,CAAC,gBAAgB,CAAC,OAAO,MAAM,CAAC,EAChC;4BACA,gBAAgB,CAAC,OAAO,MAAM,CAAC,GAAG;wBACpC;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,kCAAkC;gBAClD;gBAEA,8CAA8C;gBAC9C,iBAAiB;gBACjB,gBAAgB;gBAChB,iBAAiB;YACnB;YAEA;QACF;IACF,GAAG;QAAC,OAAO;QAAI,OAAO;QAAS,aAAa;KAAG;IAE/C,sCAAsC;IACtC,MAAM,oBAAoB,CAAC;QACzB,MAAM,aAAa,aAAa,CAAC,SAAS;QAC1C,IAAI,YAAY;YACd,kBAAkB;YAClB,yBAAyB;YACzB,qBAAqB;QACvB;IACF;IAEA,6BAA6B;IAC7B,MAAM,0BAA0B,CAAC;QAC/B,MAAM,aAAa,aAAa,CAAC,OAAO;QACxC,IAAI,YAAY;YACd,kBAAkB;YAClB,yBAAyB;YACzB,qBAAqB;QACvB;IACF;IAEA,qCAAqC;IACrC,MAAM,sBAAsB,OAAO;QACjC,IAAI,CAAC,OAAO,IAAI;QAChB,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD,EAClC,MAAM,EAAE,EACR,UACA,oHAAA,CAAA,YAAS,CAAC,SAAS;YAErB,IAAI,OAAO,OAAO,EAAE;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,oCAAoC;IACpC,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,OAAO,IAAI;QAChB,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD,EAClC,MAAM,EAAE,EACR,UACA,oHAAA,CAAA,YAAS,CAAC,MAAM;YAElB,IAAI,OAAO,OAAO,EAAE;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,0BAA0B;IAC1B,MAAM,uBAAuB,CAAC;QAC5B,gBAAgB;QAChB,kBAAkB;IACpB;IAEA,kCAAkC;IAClC,MAAM,mBAAmB;QACvB,IAAI,CAAC,OAAO,MAAM,CAAC,cAAc;QAEjC,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,EAAE,EAAE;YACjD,IAAI,OAAO,OAAO,EAAE;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,kBAAkB;YACpB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,6BAA6B;IAC7B,MAAM,2BAA2B;QAC/B,IAAI,CAAC,OAAO,MAAM,CAAC,kBAAkB;QAErC,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD,EAClC,MAAM,EAAE,EACR,kBACA,oHAAA,CAAA,YAAS,CAAC,MAAM;YAElB,IAAI,OAAO,OAAO,EAAE;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,gCAAgC;gBAChC,oBAAoB;YACtB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,kCAAkC;IAClC,MAAM,+BAA+B,CAAC;QACpC,oBAAoB;QACpB,gCAAgC;IAClC;IAEA,qBACE;;0BACE,8OAAC,kIAAA,CAAA,SAAM;gBACL,MAAM;gBACN,cAAc,CAAC;oBACb,qDAAqD;oBACrD,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,8BAA8B;wBAClG,aAAa;oBACf;gBACF;0BAEA,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;8CAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,8OAAC,kIAAA,CAAA,cAAW;oCAAC,WAAU;8CAA0B;;;;;;8CAGjD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,aAAa;;;;;;;;;;;;sCAIhC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,SAAS,IAAM,uBAAuB;;kDAEtC,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;sCAIV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;;oCAAU;oCACD,OAAO,SAAS,UAAU;oCAAE;;;;;;;;;;;;sCAIvD,8OAAC,0IAAA,CAAA,aAAU;4BAAC,WAAU;sCACnB,OAAO,SAAS,IAAI,CAAC;gCACpB,MAAM,aAAa,aAAa,CAAC,OAAO,MAAM,CAAC;gCAC/C,MAAM,WAAW,YAAY,UAAU,WACnC,WAAW,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW,KACpD;gCAEJ,qBACE,8OAAC;oCAEC,WAAU;;sDAEV,8OAAC;4CACC,WAAU;4CACV,SAAS,IAAM,kBAAkB,OAAO,MAAM;;8DAE9C,8OAAC,kIAAA,CAAA,SAAM;oDAAC,WAAU;;sEAChB,8OAAC,kIAAA,CAAA,cAAW;4DACV,KACE,YAAY,UAAU,qBAAqB;4DAE7C,WAAU;;;;;;sEAEZ,8OAAC,kIAAA,CAAA,iBAAc;4DAAC,WAAU;sEACvB;;;;;;;;;;;;8DAGL,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEACV,YAAY,UAAU,YAAY;;;;;;sEAErC,8OAAC;4DAAE,WAAU;sEACV,OAAO,IAAI,KAAK,WACb,gBACA,OAAO,IAAI,KAAK,cACd,aACA;;;;;;wDAEP,OAAO,MAAM,KAAK,aAAa,oBAC9B,8OAAC;4DAAE,WAAU;;gEAAwB;gEAC1B;gEACR,OAAO,OAAO,IAAI,cAAc,OAAO,OAAO,GAC3C,AACE,OAAO,OAAO,CAGd,QAAQ,GACV,YAAY,CAAC,OAAO,MAAM,CAAC,EAAE,UAAU,YACvC;;;;;;;wDAGP,OAAO,MAAM,KAAK,aAAa,MAC9B,OAAO,MAAM,KAAK,OAAO,2BACvB,8OAAC;4DAAE,WAAU;sEACV,OAAO,QAAQ,IACd,CAAC,SAAS,EAAE,IAAI,KAAK,OAAO,QAAQ,EAAE,kBAAkB,IAAI;;;;;;;;;;;;;;;;;;sDAMxE,8OAAC;4CAAI,WAAU;;gDAEZ,OAAO,MAAM,KAAK,aAAa,MAC9B,aAAa,CAAC,OAAO,MAAM,CAAC,KAAK,gCAC/B,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,QAAQ;oDACR,OAAM;8DAEN,cAAA,8OAAC,kMAAA,CAAA,OAAQ;wDAAC,WAAU;;;;;;;;;;;gDAKzB,OAAO,MAAM,KAAK,aAAa,oBAC9B,8OAAC,4IAAA,CAAA,eAAY;;sEACX,8OAAC,4IAAA,CAAA,sBAAmB;4DAAC,OAAO;sEAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAQ,MAAK;0EAC3B,cAAA,8OAAC,gNAAA,CAAA,iBAAc;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAG9B,8OAAC,4IAAA,CAAA,sBAAmB;4DAAC,OAAM;;gEAExB,aAAa,CAAC,OAAO,MAAM,CAAC,KAAK,wBAChC,8OAAC,4IAAA,CAAA,mBAAgB;oEACf,SAAS,IACP,wBAAwB,OAAO,MAAM;oEAEvC,UAAU,gBAAgB,CAAC,OAAO,MAAM,CAAC;8EAExC,gBAAgB,CAAC,OAAO,MAAM,CAAC,iBAC9B;;0FACE,8OAAC;gFAAI,WAAU;;;;;;4EAA6F;;qGAI9G;;0FACE,8OAAC,8MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;4EAA+B;;;;;;;;gEAQ1D,CAAC,oBAAoB,YACnB,oBAAoB,eACnB,OAAO,IAAI,KAAK,QAAS,mBAC3B;;wEACG,oBAAoB,YACnB,OAAO,IAAI,KAAK,0BACd,8OAAC,4IAAA,CAAA,mBAAgB;4EACf,SAAS,IACP,oBAAoB,OAAO,MAAM;;8FAGnC,8OAAC,sMAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;wEAIxC,oBAAoB,YACnB,OAAO,IAAI,KAAK,6BACd,8OAAC,4IAAA,CAAA,mBAAgB;4EACf,SAAS,IACP,mBAAmB,OAAO,MAAM;;8FAGlC,8OAAC,gNAAA,CAAA,YAAS;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;wEAM3C,oBAAoB,YACnB,OAAO,IAAI,KAAK,YAChB,OAAO,SAAS,SAAS,mBACvB,8OAAC,4IAAA,CAAA,mBAAgB;4EACf,SAAS,IACP,6BAA6B,OAAO,MAAM;4EAE5C,WAAU;;8FAEV,8OAAC,oMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAKxC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sFACtB,8OAAC,4IAAA,CAAA,mBAAgB;4EACf,SAAS,IACP,qBAAqB,OAAO,MAAM;4EAEpC,WAAU;;8FAEV,8OAAC,gMAAA,CAAA,MAAG;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAnJzC,OAAO,MAAM;;;;;4BA8JxB;;;;;;;;;;;;;;;;;YAML,qBAAqB,gCACpB,8OAAC,8IAAA,CAAA,UAAa;gBACZ,MAAM;gBACN,QAAQ;gBACR,cAAc,CAAC;oBACb,qBAAqB;oBACrB,IAAI,CAAC,MAAM;wBACT,kBAAkB;wBAClB,yBAAyB;oBAC3B;gBACF;gBACA,cAAc,eAAe,EAAE,KAAK,aAAa;gBACjD,8BAA8B;;;;;;YAKjC,OAAO,oBACN,8OAAC,8IAAA,CAAA,UAAe;gBACd,SAAS,MAAM,EAAE;gBACjB,QAAQ;gBACR,cAAc,CAAC;oBACb,uBAAuB;oBACvB,8EAA8E;oBAC9E,IAAI,CAAC,QAAQ,QAAQ;wBACnB,qCAAqC;wBACrC,WAAW,IAAM,aAAa,OAAO;oBACvC;gBACF;;;;;;0BAKJ,8OAAC,2IAAA,CAAA,cAAW;gBACV,MAAM;gBACN,cAAc,CAAC;oBACb,kBAAkB;oBAClB,wEAAwE;oBACxE,IAAI,CAAC,QAAQ,QAAQ;wBACnB,qCAAqC;wBACrC,WAAW,IAAM,aAAa,OAAO;oBACvC;gBACF;0BAEA,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAK1B,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;oCAAC,UAAU;8CAAc;;;;;;8CAC3C,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;4CAA0F;;uDAI3G;;;;;;;;;;;;;;;;;;;;;;;0BAQV,8OAAC,2IAAA,CAAA,cAAW;gBACV,MAAM;gBACN,cAAc,CAAC;oBACb,gCAAgC;oBAChC,4EAA4E;oBAC5E,IAAI,CAAC,QAAQ,QAAQ;wBACnB,qCAAqC;wBACrC,WAAW,IAAM,aAAa,OAAO;oBACvC;gBACF;0BAEA,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAM1B,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,UAAU;oCACV,SAAS;wCACP,gCAAgC;wCAChC,oBAAoB;oCACtB;8CACD;;;;;;8CAGD,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;4CAA0F;;uDAI3G;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}, {"offset": {"line": 2147, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/group/EditGroupNameDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON>Footer,\r\n} from \"@/components/ui/dialog\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { updateGroup } from \"@/actions/group.action\";\r\nimport { toast } from \"sonner\";\r\nimport { Group } from \"@/types/base\";\r\n\r\ninterface EditGroupNameDialogProps {\r\n  group: Group | null;\r\n  isOpen: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  onBack: () => void;\r\n  onSuccess?: (updatedGroup: Group) => void;\r\n}\r\n\r\nexport default function EditGroupNameDialog({\r\n  group,\r\n  isOpen,\r\n  onOpenChange,\r\n  onBack,\r\n  onSuccess,\r\n}: EditGroupNameDialogProps) {\r\n  const [newGroupName, setNewGroupName] = useState(group?.name || \"\");\r\n  const [isProcessing, setIsProcessing] = useState(false);\r\n\r\n  // Reset the input when the dialog opens\r\n  const handleOpenChange = (open: boolean) => {\r\n    if (open) {\r\n      setNewGroupName(group?.name || \"\");\r\n    }\r\n    onOpenChange(open);\r\n  };\r\n\r\n  const handleSubmit = async () => {\r\n    if (!group?.id) return;\r\n\r\n    // Validate input\r\n    if (!newGroupName.trim()) {\r\n      toast.error(\"Tên nhóm không được để trống\");\r\n      return;\r\n    }\r\n\r\n    setIsProcessing(true);\r\n    try {\r\n      const result = await updateGroup(group.id, { name: newGroupName.trim() });\r\n\r\n      if (result.success && result.group) {\r\n        toast.success(\"Đổi tên nhóm thành công\");\r\n        onOpenChange(false);\r\n\r\n        // Call the success callback if provided\r\n        if (onSuccess) {\r\n          onSuccess(result.group);\r\n        }\r\n      } else {\r\n        toast.error(result.error || \"Không thể đổi tên nhóm\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error updating group name:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi đổi tên nhóm\");\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={handleOpenChange}>\r\n      <DialogContent className=\"sm:max-w-[425px]\">\r\n        <DialogHeader className=\"flex flex-row items-center\">\r\n          <DialogTitle>Đổi tên nhóm</DialogTitle>\r\n        </DialogHeader>\r\n\r\n        <div className=\"flex flex-col items-center space-y-4 py-4\">\r\n          <Avatar className=\"h-20 w-20\">\r\n            <AvatarImage\r\n              src={group?.avatarUrl || undefined}\r\n              className=\"object-cover\"\r\n            />\r\n            <AvatarFallback className=\"text-xl\">\r\n              {group?.name?.slice(0, 2).toUpperCase() || \"GR\"}\r\n            </AvatarFallback>\r\n          </Avatar>\r\n\r\n          <div className=\"w-full\">\r\n            <Input\r\n              value={newGroupName}\r\n              onChange={(e) => setNewGroupName(e.target.value)}\r\n              placeholder=\"Nhập tên nhóm mới...\"\r\n              className=\"w-full\"\r\n              autoFocus\r\n            />\r\n          </div>\r\n\r\n          <p className=\"text-sm text-gray-500 text-center\">\r\n            Bạn có chắc chắn muốn đổi tên nhóm, khi xác nhận tên nhóm mới sẽ\r\n            hiển thị với tất cả thành viên.\r\n          </p>\r\n        </div>\r\n\r\n        <DialogFooter className=\"flex justify-between\">\r\n          <Button variant=\"outline\" onClick={onBack} disabled={isProcessing}>\r\n            Hủy\r\n          </Button>\r\n          <Button\r\n            onClick={handleSubmit}\r\n            disabled={\r\n              isProcessing ||\r\n              !newGroupName.trim() ||\r\n              newGroupName === group?.name\r\n            }\r\n          >\r\n            {isProcessing ? (\r\n              <>\r\n                <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\r\n                Đang xử lý...\r\n              </>\r\n            ) : (\r\n              \"Xác nhận\"\r\n            )}\r\n          </Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;AAyBe,SAAS,oBAAoB,EAC1C,KAAK,EACL,MAAM,EACN,YAAY,EACZ,MAAM,EACN,SAAS,EACgB;IACzB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,QAAQ;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,wCAAwC;IACxC,MAAM,mBAAmB,CAAC;QACxB,IAAI,MAAM;YACR,gBAAgB,OAAO,QAAQ;QACjC;QACA,aAAa;IACf;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,OAAO,IAAI;QAEhB,iBAAiB;QACjB,IAAI,CAAC,aAAa,IAAI,IAAI;YACxB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD,EAAE,MAAM,EAAE,EAAE;gBAAE,MAAM,aAAa,IAAI;YAAG;YAEvE,IAAI,OAAO,OAAO,IAAI,OAAO,KAAK,EAAE;gBAClC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,aAAa;gBAEb,wCAAwC;gBACxC,IAAI,WAAW;oBACb,UAAU,OAAO,KAAK;gBACxB;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;8BACtB,cAAA,8OAAC,kIAAA,CAAA,cAAW;kCAAC;;;;;;;;;;;8BAGf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BAAC,WAAU;;8CAChB,8OAAC,kIAAA,CAAA,cAAW;oCACV,KAAK,OAAO,aAAa;oCACzB,WAAU;;;;;;8CAEZ,8OAAC,kIAAA,CAAA,iBAAc;oCAAC,WAAU;8CACvB,OAAO,MAAM,MAAM,GAAG,GAAG,iBAAiB;;;;;;;;;;;;sCAI/C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;gCACJ,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,aAAY;gCACZ,WAAU;gCACV,SAAS;;;;;;;;;;;sCAIb,8OAAC;4BAAE,WAAU;sCAAoC;;;;;;;;;;;;8BAMnD,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAQ,UAAU;sCAAc;;;;;;sCAGnE,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UACE,gBACA,CAAC,aAAa,IAAI,MAClB,iBAAiB,OAAO;sCAGzB,6BACC;;kDACE,8OAAC;wCAAI,WAAU;;;;;;oCAA0F;;+CAI3G;;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 2343, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/group/GroupDialog.tsx"], "sourcesContent": ["\"use client\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>ontent,\r\n  <PERSON><PERSON><PERSON>eader,\r\n  DialogTitle,\r\n  DialogDescription,\r\n} from \"@/components/ui/dialog\";\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Camera,\r\n  Copy,\r\n  ExternalLink,\r\n  LogOut,\r\n  Settings,\r\n  Share2,\r\n  Video,\r\n  Trash,\r\n  Pencil,\r\n} from \"lucide-react\";\r\nimport { Group, Media, User, GroupRole } from \"@/types/base\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { useChatStore } from \"@/stores/chatStore\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { toast } from \"sonner\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport {\r\n  deleteGroup,\r\n  leaveGroup,\r\n  updateGroupAvatar,\r\n  updateMemberRole,\r\n} from \"@/actions/group.action\";\r\nimport ProfileDialog from \"@/components/profile/ProfileDialog\";\r\nimport GroupMemberList from \"./GroupMemberList\";\r\nimport AddMemberDialog from \"./AddMemberDialog\";\r\nimport EditGroupNameDialog from \"./EditGroupNameDialog\";\r\nimport { batchGetUserData } from \"@/actions/user.action\";\r\nimport GroupInfoSocketHandler from \"./GroupInfoSocketHandler\";\r\n\r\ninterface GroupDialogProps {\r\n  group: Group | null;\r\n  isOpen: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  mediaFiles?: Media[];\r\n}\r\n\r\nexport default function GroupDialog({\r\n  group,\r\n  isOpen,\r\n  onOpenChange,\r\n  mediaFiles = [],\r\n}: GroupDialogProps) {\r\n  const [showLeaveDialog, setShowLeaveDialog] = useState(false);\r\n  const [showTransferLeadershipDialog, setShowTransferLeadershipDialog] =\r\n    useState(false);\r\n  const [showConfirmTransferDialog, setShowConfirmTransferDialog] =\r\n    useState(false);\r\n  const [newLeaderId, setNewLeaderId] = useState<string | null>(null);\r\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\r\n  const [isProcessing, setIsProcessing] = useState(false);\r\n  const [selectedMember, setSelectedMember] = useState<User | null>(null);\r\n  const [showProfileDialog, setShowProfileDialog] = useState(false);\r\n  const [showMemberList, setShowMemberList] = useState(false);\r\n  const [showAddMemberDialog, setShowAddMemberDialog] = useState(false);\r\n  const [showEditNameDialog, setShowEditNameDialog] = useState(false);\r\n  const [memberDetails, setMemberDetails] = useState<{ [key: string]: User }>(\r\n    {},\r\n  );\r\n  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);\r\n  const [forceUpdate, setForceUpdate] = useState(0);\r\n  const router = useRouter();\r\n\r\n  // Function to refresh group data when socket events occur\r\n  const handleGroupUpdated = async () => {\r\n    if (group?.id) {\r\n      console.log(\"[GroupDialog] Group updated event received, refreshing data\");\r\n\r\n      // First, refresh the selected group data in the store\r\n      try {\r\n        await useChatStore.getState().refreshSelectedGroup();\r\n        console.log(\"[GroupDialog] Selected group refreshed successfully\");\r\n      } catch (error) {\r\n        console.error(\"[GroupDialog] Error refreshing selected group:\", error);\r\n      }\r\n\r\n      // Force refresh member details\r\n      const newMemberDetails: { [key: string]: User } = {};\r\n\r\n      try {\r\n        // Get the latest group data from store\r\n        const latestGroup = useChatStore.getState().selectedGroup;\r\n        const membersToProcess = latestGroup?.members || group.members || [];\r\n\r\n        // Collect all user IDs that need to be fetched\r\n        const memberIds: string[] = [];\r\n\r\n        // First, use any existing user data\r\n        for (const member of membersToProcess) {\r\n          if (member.user?.userInfo) {\r\n            newMemberDetails[member.userId] = member.user;\r\n          } else {\r\n            memberIds.push(member.userId);\r\n          }\r\n        }\r\n\r\n        // Batch fetch any missing user data\r\n        if (memberIds.length > 0) {\r\n          console.log(`[GroupDialog] Batch fetching ${memberIds.length} member details`);\r\n          const userResult = await batchGetUserData(memberIds);\r\n          if (userResult.success && userResult.users) {\r\n            userResult.users.forEach((user) => {\r\n              newMemberDetails[user.id] = user;\r\n            });\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"[GroupDialog] Error fetching member details:\", error);\r\n      }\r\n\r\n      setMemberDetails(newMemberDetails);\r\n\r\n      // Force re-render to ensure UI updates with latest data\r\n      setForceUpdate(prev => prev + 1);\r\n    }\r\n  };\r\n\r\n  // Get current user and chat store functions\r\n  const currentUser = useAuthStore((state) => state.user);\r\n  const { setSelectedGroup, selectedGroup } = useChatStore();\r\n\r\n  // Use the latest group data from store if available, otherwise fallback to prop\r\n  const currentGroup = selectedGroup && selectedGroup.id === group?.id ? selectedGroup : group;\r\n\r\n  // Determine current user's role in the group using the latest data\r\n  const currentUserRole =\r\n    currentGroup?.members?.find((member) => member.userId === currentUser?.id)?.role ||\r\n    \"MEMBER\";\r\n\r\n  // Fetch member details when group changes\r\n  useEffect(() => {\r\n    if (currentGroup?.id && currentGroup.members) {\r\n      const fetchMemberDetails = async () => {\r\n        const newMemberDetails: { [key: string]: User } = {};\r\n\r\n        try {\r\n          // Collect all user IDs that need to be fetched\r\n          const memberIds: string[] = [];\r\n\r\n          // First, use any existing user data\r\n          for (const member of currentGroup.members) {\r\n            if (member.user?.userInfo) {\r\n              newMemberDetails[member.userId] = member.user;\r\n            } else {\r\n              memberIds.push(member.userId);\r\n            }\r\n          }\r\n\r\n          // Batch fetch any missing user data\r\n          if (memberIds.length > 0) {\r\n            console.log(`Batch fetching ${memberIds.length} member details`);\r\n            const userResult = await batchGetUserData(memberIds);\r\n            if (userResult.success && userResult.users) {\r\n              userResult.users.forEach((user) => {\r\n                newMemberDetails[user.id] = user;\r\n              });\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error fetching member details:\", error);\r\n        }\r\n\r\n        setMemberDetails(newMemberDetails);\r\n      };\r\n\r\n      fetchMemberDetails();\r\n    }\r\n  }, [currentGroup?.id, currentGroup?.members, forceUpdate]);\r\n\r\n  // Handle avatar change\r\n  const handleAvatarChange = async (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (!e.target.files || e.target.files.length === 0 || !currentGroup?.id) return;\r\n\r\n    const file = e.target.files[0];\r\n    setIsUploadingAvatar(true);\r\n\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append(\"file\", file);\r\n\r\n      const result = await updateGroupAvatar(currentGroup.id, formData);\r\n\r\n      if (result.success) {\r\n        toast.success(\"Cập nhật ảnh đại diện nhóm thành công\");\r\n        // Refresh the group data or update the UI\r\n        // This could be done by refreshing the page or updating the group in the store\r\n        setTimeout(() => {\r\n          window.location.reload();\r\n        }, 500);\r\n      } else {\r\n        toast.error(result.error || \"Không thể cập nhật ảnh đại diện nhóm\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error updating group avatar:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi cập nhật ảnh đại diện nhóm\");\r\n    } finally {\r\n      setIsUploadingAvatar(false);\r\n    }\r\n  };\r\n\r\n  // Handle copy group link\r\n  const handleCopyGroupLink = () => {\r\n    if (!currentGroup?.id) return;\r\n\r\n    const groupLink = `https://zalo.me/g/${currentGroup.id}`;\r\n    navigator.clipboard.writeText(groupLink);\r\n    toast.success(\"Đã sao chép liên kết nhóm\");\r\n  };\r\n\r\n  // Hàm xử lý khi chọn thành viên để chuyển quyền trưởng nhóm\r\n  const handleSelectNewLeader = (memberId: string) => {\r\n    setNewLeaderId(memberId);\r\n    setShowConfirmTransferDialog(true);\r\n  };\r\n\r\n  // Hàm xử lý chuyển quyền trưởng nhóm\r\n  const executeTransferLeadership = async () => {\r\n    if (!currentGroup?.id || !newLeaderId) return;\r\n    setIsProcessing(true);\r\n    try {\r\n      // Chuyển quyền trưởng nhóm cho thành viên được chọn\r\n      const result = await updateMemberRole(\r\n        currentGroup.id,\r\n        newLeaderId,\r\n        GroupRole.LEADER,\r\n      );\r\n\r\n      if (result.success) {\r\n        // Đóng các dialog\r\n        setShowConfirmTransferDialog(false);\r\n        setShowTransferLeadershipDialog(false);\r\n\r\n        // Thông báo cho người dùng\r\n        toast.success(\"Đã chuyển quyền trưởng nhóm thành công\");\r\n\r\n        // Refresh group data to reflect the role change immediately\r\n        try {\r\n          await useChatStore.getState().refreshSelectedGroup();\r\n          console.log(\"[GroupDialog] Group data refreshed after leadership transfer\");\r\n        } catch (refreshError) {\r\n          console.error(\"[GroupDialog] Error refreshing group after leadership transfer:\", refreshError);\r\n        }\r\n\r\n        // Tiếp tục rời nhóm\r\n        setShowLeaveDialog(true);\r\n      } else {\r\n        toast.error(`Lỗi: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error transferring leadership:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi chuyển quyền trưởng nhóm\");\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  };\r\n\r\n  // Handle leave group\r\n  const handleLeaveGroup = async () => {\r\n    if (!currentGroup?.id) return;\r\n    setIsProcessing(true);\r\n    try {\r\n      const result = await leaveGroup(currentGroup.id);\r\n      if (result.success) {\r\n        // Close confirmation dialog\r\n        setShowLeaveDialog(false);\r\n\r\n        // Get chat store and clear cache\r\n        const chatStore = useChatStore.getState();\r\n        chatStore.clearChatCache(\"GROUP\", currentGroup.id);\r\n        chatStore.setSelectedGroup(null);\r\n\r\n        // Close group dialog\r\n        onOpenChange(false);\r\n\r\n        // Notify user\r\n        toast.success(\"Đã rời nhóm thành công\");\r\n      } else {\r\n        toast.error(`Lỗi: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error leaving group:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi rời nhóm\");\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  };\r\n\r\n  // Handle delete group\r\n  const handleDeleteGroup = async () => {\r\n    if (!currentGroup?.id) return;\r\n    setIsProcessing(true);\r\n    try {\r\n      const result = await deleteGroup(currentGroup.id);\r\n      if (result.success) {\r\n        // Close confirmation dialog\r\n        setShowDeleteDialog(false);\r\n\r\n        // Get chat store and clear cache\r\n        const chatStore = useChatStore.getState();\r\n        chatStore.clearChatCache(\"GROUP\", currentGroup.id);\r\n        chatStore.setSelectedGroup(null);\r\n\r\n        // Close group dialog\r\n        onOpenChange(false);\r\n\r\n        // Notify user\r\n        toast.success(\"Đã xóa nhóm thành công\");\r\n      } else {\r\n        toast.error(`Lỗi: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error deleting group:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi xóa nhóm\");\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Socket handler for real-time updates */}\r\n      {currentGroup && (\r\n        <GroupInfoSocketHandler\r\n          groupId={currentGroup.id}\r\n          onGroupUpdated={handleGroupUpdated}\r\n        />\r\n      )}\r\n\r\n      <Dialog open={isOpen} onOpenChange={onOpenChange}>\r\n        <DialogContent className=\"sm:max-w-[425px] h-auto !p-0 mt-0 mb-16 max-h-[90vh] overflow-y-auto no-scrollbar\">\r\n          <DialogHeader className=\"px-4 py-2 flex flex-row items-center border-b\">\r\n            <DialogTitle className=\"text-base font-semibold\">\r\n              Thông tin nhóm\r\n            </DialogTitle>\r\n            <DialogDescription className=\"sr-only\">\r\n              Xem và quản lý thông tin nhóm\r\n            </DialogDescription>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"ml-auto h-8 w-8\"\r\n              onClick={() => onOpenChange(false)}\r\n            ></Button>\r\n          </DialogHeader>\r\n\r\n          <div className=\"flex flex-col gap-2 overflow-auto no-scrollbar bg-[#e5e7eb]\">\r\n            {/* Group Avatar and Name */}\r\n            <div className=\"flex flex-col gap-2 items-center text-center px-4 py-2 bg-white\">\r\n              <div className=\"flex flex-row items-center justify-start w-full gap-4\">\r\n                <div className=\"relative\">\r\n                  <Avatar className=\"h-16 w-16 border-2\">\r\n                    <AvatarImage\r\n                      src={currentGroup?.avatarUrl || undefined}\r\n                      className=\"object-cover\"\r\n                    />\r\n                    <AvatarFallback className=\"text-xl\">\r\n                      {currentGroup?.name?.slice(0, 2).toUpperCase() || \"GR\"}\r\n                    </AvatarFallback>\r\n                  </Avatar>\r\n                  {currentUserRole === \"LEADER\" && (\r\n                    <label\r\n                      htmlFor=\"group-avatar-upload\"\r\n                      className=\"absolute bottom-0 right-0 bg-blue-500 rounded-full p-1 cursor-pointer\"\r\n                    >\r\n                      {isUploadingAvatar ? (\r\n                        <div className=\"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\r\n                      ) : (\r\n                        <Camera className=\"h-4 w-4 text-white\" />\r\n                      )}\r\n                      <input\r\n                        id=\"group-avatar-upload\"\r\n                        type=\"file\"\r\n                        accept=\"image/*\"\r\n                        className=\"hidden\"\r\n                        onChange={handleAvatarChange}\r\n                        disabled={isUploadingAvatar}\r\n                      />\r\n                    </label>\r\n                  )}\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <h2 className=\"text-base font-semibold\">{currentGroup?.name}</h2>\r\n                  {currentUserRole === \"LEADER\" && (\r\n                    <button\r\n                      className=\"text-gray-500 hover:text-blue-500 transition-colors\"\r\n                      onClick={() => setShowEditNameDialog(true)}\r\n                    >\r\n                      <Pencil className=\"h-4 w-4\" />\r\n                    </button>\r\n                  )}\r\n                </div>\r\n              </div>\r\n              {/* Message Button */}\r\n              <div\r\n                className=\"w-full bg-[#e5e7eb] py-2 px-4 text-center cursor-pointer hover:bg-gray-200\"\r\n                onClick={() => {\r\n                  if (currentGroup?.id) {\r\n                    // Close the dialog\r\n                    onOpenChange(false);\r\n\r\n                    // Open the chat with this group\r\n                    setSelectedGroup(currentGroup);\r\n\r\n                    // Navigate to chat page if not already there\r\n                    router.push(\"/dashboard/chat\");\r\n                  }\r\n                }}\r\n              >\r\n                <span className=\"font-semibold\">Nhắn tin</span>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Members Section */}\r\n            <div className=\"p-4 bg-white border-b border-gray-200\">\r\n              <div className=\"flex items-center justify-between mb-3\">\r\n                <h3 className=\"font-semibold\">\r\n                  Thành viên ({currentGroup?.members?.length || 0})\r\n                </h3>\r\n              </div>\r\n              <div className=\"flex justify-center gap-2\">\r\n                {currentGroup?.members?.slice(0, 4).map((member) => {\r\n                  const memberData = memberDetails[member.userId];\r\n                  const initials = memberData?.userInfo?.fullName\r\n                    ? memberData.userInfo.fullName.slice(0, 2).toUpperCase()\r\n                    : \"U\";\r\n                  const displayName =\r\n                    memberData?.userInfo?.fullName || \"Thành viên\";\r\n                  const isLeader = member.role === \"LEADER\";\r\n                  const isCoLeader = member.role === \"CO_LEADER\";\r\n\r\n                  return (\r\n                    <div\r\n                      key={member.userId}\r\n                      className=\"flex flex-col items-center\"\r\n                    >\r\n                      <Avatar\r\n                        className=\"h-12 w-12 mb-1 cursor-pointer\"\r\n                        onClick={() => {\r\n                          if (memberData) {\r\n                            setSelectedMember(memberData);\r\n                            setShowProfileDialog(true);\r\n                          }\r\n                        }}\r\n                      >\r\n                        <AvatarImage\r\n                          src={\r\n                            memberData?.userInfo?.profilePictureUrl || undefined\r\n                          }\r\n                          className=\"object-cover\"\r\n                        />\r\n                        <AvatarFallback>{initials}</AvatarFallback>\r\n                      </Avatar>\r\n                      <span className=\"text-xs font-medium truncate w-16 text-center\">\r\n                        {displayName}\r\n                      </span>\r\n                      {(isLeader || isCoLeader) && (\r\n                        <span className=\"text-xs text-gray-500\">\r\n                          {isLeader ? \"Trưởng nhóm\" : \"Phó nhóm\"}\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                  );\r\n                })}\r\n                {(currentGroup?.members?.length || 0) > 4 && (\r\n                  <div className=\"flex flex-col items-center\">\r\n                    <div\r\n                      className=\"h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center mb-1 cursor-pointer\"\r\n                      onClick={() => setShowMemberList(true)}\r\n                    >\r\n                      <span className=\"text-sm font-medium\">...</span>\r\n                    </div>\r\n                    <span className=\"text-xs font-medium\">Xem thêm</span>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Media Section */}\r\n            <div className=\"p-4 bg-white border-b border-gray-200\">\r\n              <div className=\"mb-3\">\r\n                <h3 className=\"font-semibold\">Ảnh/Video</h3>\r\n              </div>\r\n              {mediaFiles.length > 0 ? (\r\n                <div className=\"grid grid-cols-4 gap-1\">\r\n                  {mediaFiles.slice(0, 4).map((media, index) => (\r\n                    <div\r\n                      key={index}\r\n                      className=\"aspect-square relative overflow-hidden border border-gray-200 rounded-md cursor-pointer\"\r\n                    >\r\n                      <div\r\n                        className=\"w-full h-full bg-cover bg-center\"\r\n                        style={{ backgroundImage: `url(${media.url})` }}\r\n                      ></div>\r\n                      {media.metadata?.extension?.match(/mp4|webm|mov/i) && (\r\n                        <div className=\"absolute inset-0 flex items-center justify-center bg-black/30\">\r\n                          <Video className=\"h-6 w-6 text-white\" />\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              ) : (\r\n                <div className=\"text-center text-gray-500 py-2\">\r\n                  <p>Chưa có ảnh/video nào</p>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Link Section */}\r\n            <div className=\"p-4 bg-white border-b border-gray-200\">\r\n              <div className=\"flex items-center mb-3\">\r\n                <h3 className=\"font-semibold\">Link tham gia nhóm</h3>\r\n              </div>\r\n              <div className=\"flex items-center\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"mr-2\">\r\n                    <ExternalLink className=\"h-5 w-5 text-gray-500\" />\r\n                  </div>\r\n                  <div className=\"text-sm text-blue-500\">\r\n                    https://zalo.me/g/{currentGroup?.id || \"lqgvcn149\"}\r\n                  </div>\r\n                </div>\r\n                <div className=\"ml-auto flex\">\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    className=\"h-8 w-8 rounded-full bg-gray-200\"\r\n                    onClick={handleCopyGroupLink}\r\n                  >\r\n                    <Copy className=\"h-4 w-4\" />\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    className=\"h-8 w-8 rounded-full bg-gray-200 ml-2\"\r\n                    onClick={() => {\r\n                      if (navigator.share && currentGroup?.id) {\r\n                        navigator\r\n                          .share({\r\n                            title: `Nhóm ${currentGroup.name || \"chat\"}`,\r\n                            text: `Tham gia nhóm ${currentGroup.name || \"chat\"} trên Zalo`,\r\n                            url: `https://zalo.me/g/${currentGroup.id}`,\r\n                          })\r\n                          .catch((err) => {\r\n                            console.error(\"Error sharing:\", err);\r\n                          });\r\n                      } else {\r\n                        handleCopyGroupLink();\r\n                        toast.info(\r\n                          \"Đã sao chép liên kết. Thiết bị của bạn không hỗ trợ chia sẻ trực tiếp.\",\r\n                        );\r\n                      }\r\n                    }}\r\n                  >\r\n                    <Share2 className=\"h-4 w-4\" />\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Group Management Buttons */}\r\n            <div className=\"p-4 bg-white space-y-2\">\r\n              {/* Manage group button */}\r\n              <div\r\n                className=\"flex items-center p-2 cursor-pointer opacity-60\"\r\n                onClick={() => {\r\n                  toast.info(\"Tính năng này chưa được hỗ trợ\");\r\n                }}\r\n              >\r\n                <Settings className=\"h-5 w-5 mr-3 text-gray-500\" />\r\n                <span className=\"text-sm\">Quản lý nhóm</span>\r\n              </div>\r\n\r\n              {/* Leave group option - for everyone */}\r\n              {/* Delete group option - only for leader */}\r\n              {currentUserRole === \"LEADER\" && (\r\n                <div\r\n                  className=\"flex items-center p-2 cursor-pointer text-red-500\"\r\n                  onClick={() => setShowDeleteDialog(true)}\r\n                >\r\n                  <Trash className=\"h-5 w-5 mr-3\" />\r\n                  <span className=\"text-sm\">Xóa nhóm</span>\r\n                </div>\r\n              )}\r\n\r\n              {/* Nút rời nhóm hiển thị cho tất cả thành viên, trừ khi trưởng nhóm là thành viên duy nhất */}\r\n              {!(\r\n                currentUserRole === \"LEADER\" && currentGroup?.members?.length === 1\r\n              ) && (\r\n                <div\r\n                  className=\"flex items-center p-2 cursor-pointer text-red-500\"\r\n                  onClick={() => {\r\n                    // Nếu là trưởng nhóm, hiển thị dialog chuyển quyền trưởng nhóm\r\n                    if (currentUserRole === \"LEADER\") {\r\n                      setShowTransferLeadershipDialog(true);\r\n                    } else {\r\n                      setShowLeaveDialog(true);\r\n                    }\r\n                  }}\r\n                >\r\n                  <LogOut className=\"h-5 w-5 mr-3\" />\r\n                  <span className=\"text-sm\">Rời nhóm</span>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Profile Dialog for members */}\r\n      {showProfileDialog && selectedMember && (\r\n        <ProfileDialog\r\n          user={selectedMember}\r\n          isOpen={showProfileDialog}\r\n          onOpenChange={setShowProfileDialog}\r\n          isOwnProfile={selectedMember.id === currentUser?.id}\r\n        />\r\n      )}\r\n\r\n      {/* Group Member List Dialog */}\r\n      <GroupMemberList\r\n        group={currentGroup}\r\n        isOpen={showMemberList}\r\n        onOpenChange={setShowMemberList}\r\n        onBack={() => {\r\n          setShowMemberList(false);\r\n        }}\r\n      />\r\n\r\n      {/* Add Member Dialog */}\r\n      {currentGroup?.id && (\r\n        <AddMemberDialog\r\n          groupId={currentGroup.id}\r\n          isOpen={showAddMemberDialog}\r\n          onOpenChange={setShowAddMemberDialog}\r\n        />\r\n      )}\r\n\r\n      {/* Leave Group Confirmation Dialog */}\r\n      <AlertDialog open={showLeaveDialog} onOpenChange={setShowLeaveDialog}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Rời nhóm</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Bạn có chắc chắn muốn rời khỏi nhóm &quot;{currentGroup?.name}&quot;? Bạn\r\n              sẽ không thể xem tin nhắn trong nhóm này nữa.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={handleLeaveGroup}\r\n              disabled={isProcessing}\r\n              className=\"bg-red-500 hover:bg-red-600\"\r\n            >\r\n              {isProcessing ? (\r\n                <>\r\n                  <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\r\n                  Đang xử lý...\r\n                </>\r\n              ) : (\r\n                \"Rời nhóm\"\r\n              )}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n\r\n      {/* Dialog chuyển quyền trưởng nhóm */}\r\n      <AlertDialog\r\n        open={showTransferLeadershipDialog}\r\n        onOpenChange={setShowTransferLeadershipDialog}\r\n      >\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Chuyển quyền trưởng nhóm</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Bạn cần chuyển quyền trưởng nhóm cho một thành viên khác trước khi\r\n              rời nhóm. Vui lòng chọn một thành viên để trở thành trưởng nhóm\r\n              mới.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <div className=\"max-h-[200px] overflow-y-auto my-4 border rounded-md\">\r\n            {currentGroup?.members\r\n              ?.filter((member) => member.userId !== currentUser?.id) // Lọc ra các thành viên khác\r\n              .map((member) => {\r\n                const memberData = memberDetails[member.userId];\r\n                const initials = memberData?.userInfo?.fullName\r\n                  ? memberData.userInfo.fullName.slice(0, 2).toUpperCase()\r\n                  : \"??\";\r\n\r\n                return (\r\n                  <div\r\n                    key={member.userId}\r\n                    className=\"flex items-center p-3 hover:bg-gray-100 cursor-pointer\"\r\n                    onClick={() => handleSelectNewLeader(member.userId)}\r\n                  >\r\n                    <Avatar className=\"h-8 w-8 mr-3\">\r\n                      <AvatarImage\r\n                        src={\r\n                          memberData?.userInfo?.profilePictureUrl || undefined\r\n                        }\r\n                        className=\"object-cover\"\r\n                      />\r\n                      <AvatarFallback className=\"bg-gray-200 text-gray-600\">\r\n                        {initials}\r\n                      </AvatarFallback>\r\n                    </Avatar>\r\n                    <div>\r\n                      <p className=\"font-medium\">\r\n                        {memberData?.userInfo?.fullName || \"Thành viên\"}\r\n                      </p>\r\n                      <p className=\"text-xs text-gray-500\">\r\n                        {member.role === \"CO_LEADER\"\r\n                          ? \"Phó nhóm\"\r\n                          : \"Thành viên\"}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                );\r\n              })}\r\n          </div>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n\r\n      {/* Dialog xác nhận chuyển quyền trưởng nhóm */}\r\n      <AlertDialog\r\n        open={showConfirmTransferDialog}\r\n        onOpenChange={setShowConfirmTransferDialog}\r\n      >\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>\r\n              Xác nhận chuyển quyền trưởng nhóm\r\n            </AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              {newLeaderId && memberDetails[newLeaderId] ? (\r\n                <>\r\n                  Bạn có chắc chắn muốn chuyển quyền trưởng nhóm cho{\" \"}\r\n                  <strong>\r\n                    {memberDetails[newLeaderId]?.userInfo?.fullName ||\r\n                      \"Thành viên này\"}\r\n                  </strong>\r\n                  ?\r\n                  <br />\r\n                  Sau khi chuyển quyền, bạn sẽ trở thành thành viên thường trong\r\n                  nhóm.\r\n                </>\r\n              ) : (\r\n                \"Bạn có chắc chắn muốn chuyển quyền trưởng nhóm cho thành viên này?\"\r\n              )}\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel\r\n              disabled={isProcessing}\r\n              onClick={() => {\r\n                setShowConfirmTransferDialog(false);\r\n                setNewLeaderId(null);\r\n              }}\r\n            >\r\n              Hủy\r\n            </AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={executeTransferLeadership}\r\n              disabled={isProcessing}\r\n              className=\"bg-blue-500 hover:bg-blue-600\"\r\n            >\r\n              {isProcessing ? (\r\n                <>\r\n                  <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\r\n                  Đang xử lý...\r\n                </>\r\n              ) : (\r\n                \"Xác nhận\"\r\n              )}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n\r\n      {/* Edit Group Name Dialog */}\r\n      {currentGroup && (\r\n        <EditGroupNameDialog\r\n          group={currentGroup}\r\n          isOpen={showEditNameDialog}\r\n          onOpenChange={setShowEditNameDialog}\r\n          onBack={() => setShowEditNameDialog(false)}\r\n          onSuccess={(updatedGroup) => {\r\n            // Update the group in the store\r\n            const chatStore = useChatStore.getState();\r\n            if (chatStore.selectedGroup?.id === updatedGroup.id) {\r\n              chatStore.setSelectedGroup(updatedGroup);\r\n            }\r\n\r\n            // Refresh the page after a short delay to ensure all components are updated\r\n            setTimeout(() => {\r\n              window.location.reload();\r\n            }, 500);\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {/* Delete Group Confirmation Dialog */}\r\n      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Xóa nhóm</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Bạn có chắc chắn muốn xóa nhóm &quot;{currentGroup?.name}&quot;? Hành\r\n              động này không thể hoàn tác và tất cả tin nhắn trong nhóm sẽ bị\r\n              xóa.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={handleDeleteGroup}\r\n              disabled={isProcessing}\r\n              className=\"bg-red-500 hover:bg-red-600\"\r\n            >\r\n              {isProcessing ? (\r\n                <>\r\n                  <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\r\n                  Đang xử lý...\r\n                </>\r\n              ) : (\r\n                \"Xóa nhóm\"\r\n              )}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AAOA;AAUA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AAhDA;;;;;;;;;;;;;;;;;;;;AAyDe,SAAS,YAAY,EAClC,KAAK,EACL,MAAM,EACN,YAAY,EACZ,aAAa,EAAE,EACE;IACjB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,8BAA8B,gCAAgC,GACnE,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACX,MAAM,CAAC,2BAA2B,6BAA6B,GAC7D,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACX,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAClE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC/C,CAAC;IAEH,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,0DAA0D;IAC1D,MAAM,qBAAqB;QACzB,IAAI,OAAO,IAAI;YACb,QAAQ,GAAG,CAAC;YAEZ,sDAAsD;YACtD,IAAI;gBACF,MAAM,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,oBAAoB;gBAClD,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kDAAkD;YAClE;YAEA,+BAA+B;YAC/B,MAAM,mBAA4C,CAAC;YAEnD,IAAI;gBACF,uCAAuC;gBACvC,MAAM,cAAc,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,aAAa;gBACzD,MAAM,mBAAmB,aAAa,WAAW,MAAM,OAAO,IAAI,EAAE;gBAEpE,+CAA+C;gBAC/C,MAAM,YAAsB,EAAE;gBAE9B,oCAAoC;gBACpC,KAAK,MAAM,UAAU,iBAAkB;oBACrC,IAAI,OAAO,IAAI,EAAE,UAAU;wBACzB,gBAAgB,CAAC,OAAO,MAAM,CAAC,GAAG,OAAO,IAAI;oBAC/C,OAAO;wBACL,UAAU,IAAI,CAAC,OAAO,MAAM;oBAC9B;gBACF;gBAEA,oCAAoC;gBACpC,IAAI,UAAU,MAAM,GAAG,GAAG;oBACxB,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,UAAU,MAAM,CAAC,eAAe,CAAC;oBAC7E,MAAM,aAAa,MAAM,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE;oBAC1C,IAAI,WAAW,OAAO,IAAI,WAAW,KAAK,EAAE;wBAC1C,WAAW,KAAK,CAAC,OAAO,CAAC,CAAC;4BACxB,gBAAgB,CAAC,KAAK,EAAE,CAAC,GAAG;wBAC9B;oBACF;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gDAAgD;YAChE;YAEA,iBAAiB;YAEjB,wDAAwD;YACxD,eAAe,CAAA,OAAQ,OAAO;QAChC;IACF;IAEA,4CAA4C;IAC5C,MAAM,cAAc,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,IAAI;IACtD,MAAM,EAAE,gBAAgB,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IAEvD,gFAAgF;IAChF,MAAM,eAAe,iBAAiB,cAAc,EAAE,KAAK,OAAO,KAAK,gBAAgB;IAEvF,mEAAmE;IACnE,MAAM,kBACJ,cAAc,SAAS,KAAK,CAAC,SAAW,OAAO,MAAM,KAAK,aAAa,KAAK,QAC5E;IAEF,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,MAAM,aAAa,OAAO,EAAE;YAC5C,MAAM,qBAAqB;gBACzB,MAAM,mBAA4C,CAAC;gBAEnD,IAAI;oBACF,+CAA+C;oBAC/C,MAAM,YAAsB,EAAE;oBAE9B,oCAAoC;oBACpC,KAAK,MAAM,UAAU,aAAa,OAAO,CAAE;wBACzC,IAAI,OAAO,IAAI,EAAE,UAAU;4BACzB,gBAAgB,CAAC,OAAO,MAAM,CAAC,GAAG,OAAO,IAAI;wBAC/C,OAAO;4BACL,UAAU,IAAI,CAAC,OAAO,MAAM;wBAC9B;oBACF;oBAEA,oCAAoC;oBACpC,IAAI,UAAU,MAAM,GAAG,GAAG;wBACxB,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,UAAU,MAAM,CAAC,eAAe,CAAC;wBAC/D,MAAM,aAAa,MAAM,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE;wBAC1C,IAAI,WAAW,OAAO,IAAI,WAAW,KAAK,EAAE;4BAC1C,WAAW,KAAK,CAAC,OAAO,CAAC,CAAC;gCACxB,gBAAgB,CAAC,KAAK,EAAE,CAAC,GAAG;4BAC9B;wBACF;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,kCAAkC;gBAClD;gBAEA,iBAAiB;YACnB;YAEA;QACF;IACF,GAAG;QAAC,cAAc;QAAI,cAAc;QAAS;KAAY;IAEzD,uBAAuB;IACvB,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,cAAc,IAAI;QAEzE,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QAC9B,qBAAqB;QAErB,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE,aAAa,EAAE,EAAE;YAExD,IAAI,OAAO,OAAO,EAAE;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,0CAA0C;gBAC1C,+EAA+E;gBAC/E,WAAW;oBACT,OAAO,QAAQ,CAAC,MAAM;gBACxB,GAAG;YACL,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,yBAAyB;IACzB,MAAM,sBAAsB;QAC1B,IAAI,CAAC,cAAc,IAAI;QAEvB,MAAM,YAAY,CAAC,kBAAkB,EAAE,aAAa,EAAE,EAAE;QACxD,UAAU,SAAS,CAAC,SAAS,CAAC;QAC9B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,4DAA4D;IAC5D,MAAM,wBAAwB,CAAC;QAC7B,eAAe;QACf,6BAA6B;IAC/B;IAEA,qCAAqC;IACrC,MAAM,4BAA4B;QAChC,IAAI,CAAC,cAAc,MAAM,CAAC,aAAa;QACvC,gBAAgB;QAChB,IAAI;YACF,oDAAoD;YACpD,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD,EAClC,aAAa,EAAE,EACf,aACA,oHAAA,CAAA,YAAS,CAAC,MAAM;YAGlB,IAAI,OAAO,OAAO,EAAE;gBAClB,kBAAkB;gBAClB,6BAA6B;gBAC7B,gCAAgC;gBAEhC,2BAA2B;gBAC3B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,4DAA4D;gBAC5D,IAAI;oBACF,MAAM,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,oBAAoB;oBAClD,QAAQ,GAAG,CAAC;gBACd,EAAE,OAAO,cAAc;oBACrB,QAAQ,KAAK,CAAC,mEAAmE;gBACnF;gBAEA,oBAAoB;gBACpB,mBAAmB;YACrB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAmB;QACvB,IAAI,CAAC,cAAc,IAAI;QACvB,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD,EAAE,aAAa,EAAE;YAC/C,IAAI,OAAO,OAAO,EAAE;gBAClB,4BAA4B;gBAC5B,mBAAmB;gBAEnB,iCAAiC;gBACjC,MAAM,YAAY,0HAAA,CAAA,eAAY,CAAC,QAAQ;gBACvC,UAAU,cAAc,CAAC,SAAS,aAAa,EAAE;gBACjD,UAAU,gBAAgB,CAAC;gBAE3B,qBAAqB;gBACrB,aAAa;gBAEb,cAAc;gBACd,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,sBAAsB;IACtB,MAAM,oBAAoB;QACxB,IAAI,CAAC,cAAc,IAAI;QACvB,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD,EAAE,aAAa,EAAE;YAChD,IAAI,OAAO,OAAO,EAAE;gBAClB,4BAA4B;gBAC5B,oBAAoB;gBAEpB,iCAAiC;gBACjC,MAAM,YAAY,0HAAA,CAAA,eAAY,CAAC,QAAQ;gBACvC,UAAU,cAAc,CAAC,SAAS,aAAa,EAAE;gBACjD,UAAU,gBAAgB,CAAC;gBAE3B,qBAAqB;gBACrB,aAAa;gBAEb,cAAc;gBACd,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE;;YAEG,8BACC,8OAAC,qJAAA,CAAA,UAAsB;gBACrB,SAAS,aAAa,EAAE;gBACxB,gBAAgB;;;;;;0BAIpB,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAQ,cAAc;0BAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,kIAAA,CAAA,cAAW;oCAAC,WAAU;8CAA0B;;;;;;8CAGjD,8OAAC,kIAAA,CAAA,oBAAiB;oCAAC,WAAU;8CAAU;;;;;;8CAGvC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,aAAa;;;;;;;;;;;;sCAIhC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DAAC,WAAU;;8EAChB,8OAAC,kIAAA,CAAA,cAAW;oEACV,KAAK,cAAc,aAAa;oEAChC,WAAU;;;;;;8EAEZ,8OAAC,kIAAA,CAAA,iBAAc;oEAAC,WAAU;8EACvB,cAAc,MAAM,MAAM,GAAG,GAAG,iBAAiB;;;;;;;;;;;;wDAGrD,oBAAoB,0BACnB,8OAAC;4DACC,SAAQ;4DACR,WAAU;;gEAET,kCACC,8OAAC;oEAAI,WAAU;;;;;yFAEf,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAEpB,8OAAC;oEACC,IAAG;oEACH,MAAK;oEACL,QAAO;oEACP,WAAU;oEACV,UAAU;oEACV,UAAU;;;;;;;;;;;;;;;;;;8DAKlB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA2B,cAAc;;;;;;wDACtD,oBAAoB,0BACnB,8OAAC;4DACC,WAAU;4DACV,SAAS,IAAM,sBAAsB;sEAErC,cAAA,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAM1B,8OAAC;4CACC,WAAU;4CACV,SAAS;gDACP,IAAI,cAAc,IAAI;oDACpB,mBAAmB;oDACnB,aAAa;oDAEb,gCAAgC;oDAChC,iBAAiB;oDAEjB,6CAA6C;oDAC7C,OAAO,IAAI,CAAC;gDACd;4CACF;sDAEA,cAAA,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;;8CAKpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;;oDAAgB;oDACf,cAAc,SAAS,UAAU;oDAAE;;;;;;;;;;;;sDAGpD,8OAAC;4CAAI,WAAU;;gDACZ,cAAc,SAAS,MAAM,GAAG,GAAG,IAAI,CAAC;oDACvC,MAAM,aAAa,aAAa,CAAC,OAAO,MAAM,CAAC;oDAC/C,MAAM,WAAW,YAAY,UAAU,WACnC,WAAW,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW,KACpD;oDACJ,MAAM,cACJ,YAAY,UAAU,YAAY;oDACpC,MAAM,WAAW,OAAO,IAAI,KAAK;oDACjC,MAAM,aAAa,OAAO,IAAI,KAAK;oDAEnC,qBACE,8OAAC;wDAEC,WAAU;;0EAEV,8OAAC,kIAAA,CAAA,SAAM;gEACL,WAAU;gEACV,SAAS;oEACP,IAAI,YAAY;wEACd,kBAAkB;wEAClB,qBAAqB;oEACvB;gEACF;;kFAEA,8OAAC,kIAAA,CAAA,cAAW;wEACV,KACE,YAAY,UAAU,qBAAqB;wEAE7C,WAAU;;;;;;kFAEZ,8OAAC,kIAAA,CAAA,iBAAc;kFAAE;;;;;;;;;;;;0EAEnB,8OAAC;gEAAK,WAAU;0EACb;;;;;;4DAEF,CAAC,YAAY,UAAU,mBACtB,8OAAC;gEAAK,WAAU;0EACb,WAAW,gBAAgB;;;;;;;uDAzB3B,OAAO,MAAM;;;;;gDA8BxB;gDACC,CAAC,cAAc,SAAS,UAAU,CAAC,IAAI,mBACtC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAU;4DACV,SAAS,IAAM,kBAAkB;sEAEjC,cAAA,8OAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;sEAExC,8OAAC;4DAAK,WAAU;sEAAsB;;;;;;;;;;;;;;;;;;;;;;;;8CAO9C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAAgB;;;;;;;;;;;wCAE/B,WAAW,MAAM,GAAG,kBACnB,8OAAC;4CAAI,WAAU;sDACZ,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBAClC,8OAAC;oDAEC,WAAU;;sEAEV,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,iBAAiB,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;4DAAC;;;;;;wDAE/C,MAAM,QAAQ,EAAE,WAAW,MAAM,kCAChC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;mDAThB;;;;;;;;;iEAgBX,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;0DAAE;;;;;;;;;;;;;;;;;8CAMT,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAAgB;;;;;;;;;;;sDAEhC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,sNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;sEAE1B,8OAAC;4DAAI,WAAU;;gEAAwB;gEAClB,cAAc,MAAM;;;;;;;;;;;;;8DAG3C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;4DACV,SAAS;sEAET,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;4DACV,SAAS;gEACP,IAAI,UAAU,KAAK,IAAI,cAAc,IAAI;oEACvC,UACG,KAAK,CAAC;wEACL,OAAO,CAAC,KAAK,EAAE,aAAa,IAAI,IAAI,QAAQ;wEAC5C,MAAM,CAAC,cAAc,EAAE,aAAa,IAAI,IAAI,OAAO,UAAU,CAAC;wEAC9D,KAAK,CAAC,kBAAkB,EAAE,aAAa,EAAE,EAAE;oEAC7C,GACC,KAAK,CAAC,CAAC;wEACN,QAAQ,KAAK,CAAC,kBAAkB;oEAClC;gEACJ,OAAO;oEACL;oEACA,wIAAA,CAAA,QAAK,CAAC,IAAI,CACR;gEAEJ;4DACF;sEAEA,cAAA,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO1B,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CACC,WAAU;4CACV,SAAS;gDACP,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC;4CACb;;8DAEA,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;wCAK3B,oBAAoB,0BACnB,8OAAC;4CACC,WAAU;4CACV,SAAS,IAAM,oBAAoB;;8DAEnC,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;wCAK7B,CAAC,CACA,oBAAoB,YAAY,cAAc,SAAS,WAAW,CACpE,mBACE,8OAAC;4CACC,WAAU;4CACV,SAAS;gDACP,+DAA+D;gDAC/D,IAAI,oBAAoB,UAAU;oDAChC,gCAAgC;gDAClC,OAAO;oDACL,mBAAmB;gDACrB;4CACF;;8DAEA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASrC,qBAAqB,gCACpB,8OAAC,8IAAA,CAAA,UAAa;gBACZ,MAAM;gBACN,QAAQ;gBACR,cAAc;gBACd,cAAc,eAAe,EAAE,KAAK,aAAa;;;;;;0BAKrD,8OAAC,8IAAA,CAAA,UAAe;gBACd,OAAO;gBACP,QAAQ;gBACR,cAAc;gBACd,QAAQ;oBACN,kBAAkB;gBACpB;;;;;;YAID,cAAc,oBACb,8OAAC,8IAAA,CAAA,UAAe;gBACd,SAAS,aAAa,EAAE;gBACxB,QAAQ;gBACR,cAAc;;;;;;0BAKlB,8OAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAiB,cAAc;0BAChD,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;;wCAAC;wCACqB,cAAc;wCAAK;;;;;;;;;;;;;sCAIlE,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;oCAAC,UAAU;8CAAc;;;;;;8CAC3C,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;4CAA0F;;uDAI3G;;;;;;;;;;;;;;;;;;;;;;;0BAQV,8OAAC,2IAAA,CAAA,cAAW;gBACV,MAAM;gBACN,cAAc;0BAEd,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAM1B,8OAAC;4BAAI,WAAU;sCACZ,cAAc,SACX,OAAO,CAAC,SAAW,OAAO,MAAM,KAAK,aAAa,IAAI,6BAA6B;6BACpF,IAAI,CAAC;gCACJ,MAAM,aAAa,aAAa,CAAC,OAAO,MAAM,CAAC;gCAC/C,MAAM,WAAW,YAAY,UAAU,WACnC,WAAW,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW,KACpD;gCAEJ,qBACE,8OAAC;oCAEC,WAAU;oCACV,SAAS,IAAM,sBAAsB,OAAO,MAAM;;sDAElD,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,kIAAA,CAAA,cAAW;oDACV,KACE,YAAY,UAAU,qBAAqB;oDAE7C,WAAU;;;;;;8DAEZ,8OAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACvB;;;;;;;;;;;;sDAGL,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DACV,YAAY,UAAU,YAAY;;;;;;8DAErC,8OAAC;oDAAE,WAAU;8DACV,OAAO,IAAI,KAAK,cACb,aACA;;;;;;;;;;;;;mCAtBH,OAAO,MAAM;;;;;4BA2BxB;;;;;;sCAEJ,8OAAC,2IAAA,CAAA,oBAAiB;sCAChB,cAAA,8OAAC,2IAAA,CAAA,oBAAiB;gCAAC,UAAU;0CAAc;;;;;;;;;;;;;;;;;;;;;;0BAMjD,8OAAC,2IAAA,CAAA,cAAW;gBACV,MAAM;gBACN,cAAc;0BAEd,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAGlB,8OAAC,2IAAA,CAAA,yBAAsB;8CACpB,eAAe,aAAa,CAAC,YAAY,iBACxC;;4CAAE;4CACmD;0DACnD,8OAAC;0DACE,aAAa,CAAC,YAAY,EAAE,UAAU,YACrC;;;;;;4CACK;0DAET,8OAAC;;;;;4CAAK;;uDAKR;;;;;;;;;;;;sCAIN,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,UAAU;oCACV,SAAS;wCACP,6BAA6B;wCAC7B,eAAe;oCACjB;8CACD;;;;;;8CAGD,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;4CAA0F;;uDAI3G;;;;;;;;;;;;;;;;;;;;;;;YAQT,8BACC,8OAAC,kJAAA,CAAA,UAAmB;gBAClB,OAAO;gBACP,QAAQ;gBACR,cAAc;gBACd,QAAQ,IAAM,sBAAsB;gBACpC,WAAW,CAAC;oBACV,gCAAgC;oBAChC,MAAM,YAAY,0HAAA,CAAA,eAAY,CAAC,QAAQ;oBACvC,IAAI,UAAU,aAAa,EAAE,OAAO,aAAa,EAAE,EAAE;wBACnD,UAAU,gBAAgB,CAAC;oBAC7B;oBAEA,4EAA4E;oBAC5E,WAAW;wBACT,OAAO,QAAQ,CAAC,MAAM;oBACxB,GAAG;gBACL;;;;;;0BAKJ,8OAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAkB,cAAc;0BACjD,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;;wCAAC;wCACgB,cAAc;wCAAK;;;;;;;;;;;;;sCAK7D,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;oCAAC,UAAU;8CAAc;;;;;;8CAC3C,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;4CAA0F;;uDAI3G;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}]}