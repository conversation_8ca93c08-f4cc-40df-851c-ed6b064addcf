module.exports = {

"[project]/src/actions/auth.action.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_actions_0af64bfa._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/actions/auth.action.ts [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/src/actions/group.action.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/actions/group.action.ts [app-ssr] (ecmascript)");
    });
});
}}),

};